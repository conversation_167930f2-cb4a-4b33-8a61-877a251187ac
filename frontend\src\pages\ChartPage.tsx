/**
 * 图表页面组件
 * 
 * 该页面展示了如何使用拆分后的图表组件架构。
 * 包括数据加载、管理和展示。
 */

import React, { useState, useEffect, useCallback } from 'react';
import { IChartApi, ISeriesApi } from 'lightweight-charts';
import ChartComposite from '../components/chart/ChartComposite';
import { KLineData, PredictionData } from '../types/chartTypes';
import { fetchCandleData } from '../services/api';

/**
 * 图表页面组件
 */
const ChartPage: React.FC = () => {
  // 状态管理
  const [candleData, setCandleData] = useState<KLineData[]>([]);
  const [predictionData, setPredictionData] = useState<PredictionData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [chartType, setChartType] = useState<'candlestick' | 'line'>('candlestick');
  const [showPrediction, setShowPrediction] = useState(true);
  const [showPredictionLine, setShowPredictionLine] = useState(true);
  
  // 加载K线数据
  const loadCandleData = useCallback(async () => {
    setIsLoading(true);
    try {
      const data = await fetchCandleData();
      setCandleData(data);
      console.log(`加载了 ${data.length} 条K线数据`);
      
      // 模拟加载预测数据 - 实际应用中这里应该调用真实的API
      const mockPredictions: PredictionData[] = data.slice(0, 20).map(candle => ({
        ...candle,
        isActive: false
      }));
      
      // 设置最后一条为活跃预测
      if (mockPredictions.length > 0) {
        mockPredictions[mockPredictions.length - 1].isActive = true;
      }
      
      setPredictionData(mockPredictions);
      console.log(`加载了 ${mockPredictions.length} 条预测数据`);
    } catch (error) {
      console.error('加载K线数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // 加载更多历史K线数据
  const handleLoadMoreCandleHistory = useCallback((oldestTime: number) => {
    console.log(`需要加载更早于 ${new Date(oldestTime * 1000).toISOString()} 的K线数据`);
    // 实际应用中这里应该调用API加载更多历史数据
  }, []);
  
  // 加载更多历史预测数据
  const handleLoadMorePredictionHistory = useCallback((oldestTime: number) => {
    console.log(`需要加载更早于 ${new Date(oldestTime).toISOString()} 的预测数据`);
    // 实际应用中这里应该调用API加载更多历史预测数据
  }, []);
  
  // 图表类型切换处理
  const handleChartTypeChange = useCallback((type: 'candlestick' | 'line') => {
    setChartType(type);
    console.log(`图表类型已切换为: ${type}`);
  }, []);
  
  // 图表就绪回调
  const handleChartReady = useCallback((chart: IChartApi, mainSeries: ISeriesApi<"Candlestick">) => {
    console.log('图表和主K线系列已就绪');
    // 这里可以做一些图表就绪后的初始化工作
  }, []);
  
  // 组件挂载时加载数据
  useEffect(() => {
    loadCandleData();
  }, [loadCandleData]);

  // 渲染页面
  return (
    <div className="chart-page p-4 bg-background min-h-screen">
      <h1 className="text-xl text-content-primary mb-4">BTC/USDT 市场图表</h1>
      
      {/* 控制面板 */}
      <div className="control-panel mb-4 flex space-x-4">
        <button 
          className={`px-3 py-1 rounded ${chartType === 'candlestick' ? 'bg-primary' : 'bg-background-input'}`}
          onClick={() => handleChartTypeChange('candlestick')}
        >
          K线图
        </button>
        <button 
          className={`px-3 py-1 rounded ${chartType === 'line' ? 'bg-primary' : 'bg-background-input'}`}
          onClick={() => handleChartTypeChange('line')}
        >
          折线图
        </button>
        <div className="flex items-center ml-4">
          <input 
            type="checkbox" 
            id="showPrediction" 
            checked={showPrediction} 
            onChange={e => setShowPrediction(e.target.checked)}
          />
          <label htmlFor="showPrediction" className="ml-2 text-content-primary">显示预测K线</label>
        </div>
        <div className="flex items-center">
          <input 
            type="checkbox" 
            id="showPredictionLine" 
            checked={showPredictionLine} 
            onChange={e => setShowPredictionLine(e.target.checked)}
          />
          <label htmlFor="showPredictionLine" className="ml-2 text-content-primary">显示预测折线</label>
        </div>
        <button 
          className="px-3 py-1 rounded bg-success"
          onClick={loadCandleData}
        >
          刷新数据
        </button>
      </div>
      
      {/* 图表组件 */}
      <div className="chart-container bg-background-card rounded-lg overflow-hidden border border-border shadow-card">
        <ChartComposite 
          candleData={candleData}
          predictionData={predictionData}
          isLoading={isLoading}
          onLoadMoreHistory={handleLoadMorePredictionHistory}
          onLoadMoreCandleHistory={handleLoadMoreCandleHistory}
          onChartReady={handleChartReady}
          showPrediction={showPrediction}
          showPredictionLine={showPredictionLine}
          chartType={chartType}
          symbol="BTC/USDT"
          timeframe="30M"
          onChartTypeChange={handleChartTypeChange}
        />
      </div>
    </div>
  );
};

export default ChartPage; 