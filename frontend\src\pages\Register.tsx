import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import AuthForm from '../components/AuthForm';
import useUserStore from '../store/useUserStore';
import configService from '../services/configService';

const Register: React.FC = () => {
  const navigate = useNavigate();
  const { register, isLoading, error, isAuthenticated, clearError, isInitializing } = useUserStore();
  const [registrationSuccess, setRegistrationSuccess] = useState(false);
  const [inviteCodeRequired, setInviteCodeRequired] = useState(false);
  const [passwordMinLength, setPasswordMinLength] = useState(6);
  const [isRegistrationEnabled, setIsRegistrationEnabled] = useState(true);
  const [isConfigLoading, setIsConfigLoading] = useState(true);

  // 如果用户已登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  // 清除之前的错误
  useEffect(() => {
    clearError();
  }, [clearError]);

  // 加载注册配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setIsConfigLoading(true);

        // 获取注册配置
        const config = await configService.getConfig();
        const userSettings = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:4000/api'}/config/user-settings`)
          .then(res => res.json())
          .then(data => data.success ? data.settings : null)
          .catch(() => null);

        // 如果API返回了用户设置，使用API返回的值
        if (userSettings) {
          setInviteCodeRequired(userSettings.inviteCodeRequired);
          setPasswordMinLength(userSettings.passwordMinLength);
          setIsRegistrationEnabled(userSettings.enableRegistration);
        } else {
          // 否则使用默认值
          setInviteCodeRequired(false);
          setPasswordMinLength(6);
          setIsRegistrationEnabled(true);
        }
      } catch (error) {
        console.error('加载注册配置失败:', error);
        // 使用默认值
        setInviteCodeRequired(false);
        setPasswordMinLength(6);
        setIsRegistrationEnabled(true);
      } finally {
        setIsConfigLoading(false);
      }
    };

    loadConfig();
  }, []);

  // 处理注册表单提交
  const handleSubmit = async (formData: Record<string, string>) => {
    try {
      await register(formData.email, formData.password, formData.inviteCode);
      // 注册成功
      setRegistrationSuccess(true);
      // 2秒后重定向到登录页
      setTimeout(() => {
        navigate('/login', {
          state: {
            message: '注册成功，请查收验证邮件并完成验证后登录'
          }
        });
      }, 2000);
    } catch (error) {
      // 错误已在store中处理
      console.error('注册失败', error);
    }
  };

  // 注册表单字段配置
  const fields = [
    {
      name: 'email',
      label: '邮箱',
      type: 'email',
      placeholder: '请输入邮箱地址'
    },
    {
      name: 'password',
      label: '密码',
      type: 'password',
      placeholder: `请输入密码 (至少${passwordMinLength}位)`
    },
    {
      name: 'inviteCode',
      label: inviteCodeRequired ? '邀请码' : '邀请码 (可选)',
      type: 'text',
      placeholder: inviteCodeRequired ? '请输入邀请码' : '如有邀请码请输入',
      required: inviteCodeRequired
    }
  ];

  // 如果正在初始化或加载配置，显示加载状态
  if (isInitializing || isConfigLoading) {
    return <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>;
  }

  // 如果注册功能已关闭，显示提示信息
  if (!isRegistrationEnabled) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background px-4">
        <div className="w-full max-w-md bg-white dark:bg-background-card p-8 rounded-lg shadow-card text-center">
          <div className="text-yellow-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
              <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <h2 className="text-2xl font-bold mb-4 text-gray-800 dark:text-content-primary">注册功能已关闭</h2>
          <p className="text-content-muted dark:text-content-secondary mb-4">系统当前不接受新用户注册，请稍后再试或联系管理员</p>
          <Link to="/login" className="text-blue-600 hover:underline">
            返回登录
          </Link>
        </div>
      </div>
    );
  }

  // 显示注册成功信息
  if (registrationSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background px-4">
        <div className="w-full max-w-md bg-white dark:bg-background-card p-8 rounded-lg shadow-card text-center">
          <div className="text-green-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
              <path d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h2 className="text-2xl font-bold mb-4 text-gray-800 dark:text-content-primary">注册成功！</h2>
          <p className="text-content-muted dark:text-content-secondary mb-4">请查收验证邮件并完成验证后登录</p>
          <div className="animate-pulse">正在跳转到登录页面...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background px-4">
      <div className="w-full max-w-md">
        <AuthForm
          title="注册"
          fields={fields}
          submitText="注册"
          onSubmit={handleSubmit}
          isLoading={isLoading}
          error={error}
          footerText={
            <div>
              已有账号？{' '}
              <Link to="/login" className="text-blue-600 hover:underline">
                立即登录
              </Link>
            </div>
          }
        />
      </div>
    </div>
  );
};

export default Register;