import React, { Component, ErrorInfo, ReactNode, useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { useTokenRefresh } from './hooks/useTokenRefresh';
import './App.css';
import './i18n';
import LandingPage from './pages/LandingPage';
import Login from './pages/Login';
import Register from './pages/Register';
import VerifySuccess from './pages/VerifySuccess';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import UserCenter from './pages/UserCenter';
import Dashboard from './pages/Dashboard';
import Subscribe from './pages/Subscribe';
import SubscriptionSuccess from './pages/SubscriptionSuccess';
import PrivateRoute from './routes/PrivateRoute';
import AdminRoute from './routes/AdminRoute';
import useUserStore from './store/useUserStore';
import useAdminStore from './store/useAdminStore';
import PublicLayout from './layouts/PublicLayout';
import AppLayout from './layouts/AppLayout';
import SubscriptionLayout from './layouts/SubscriptionLayout';
import { Toaster } from './components/ui/toaster';
import tokenService from './api/tokenService';
import configService from './services/configService';

// 管理员页面
import AdminLogin from './pages/admin/Login';
import AdminDashboard from './pages/admin/Dashboard';
import AdminUsers from './pages/admin/Users';
import AdminAnnouncements from './pages/admin/Announcements';
import AdminPayments from './pages/admin/Payments';
import AdminPaymentDetails from './pages/admin/PaymentDetails';
import AdminFeedback from './pages/admin/Feedback';
import AdminFeedbackDetail from './pages/admin/FeedbackDetail';
import SettingsIndex from './pages/admin/settings/SettingsIndex';
import BasicSettings from './pages/admin/settings/BasicSettings';
import UserSettings from './pages/admin/settings/UserSettings';
import PaymentSettings from './pages/admin/settings/PaymentSettings';
import SecuritySettings from './pages/admin/settings/SecuritySettings';

// 错误边界组件
class ErrorBoundary extends Component<{ children: ReactNode }, { hasError: boolean }> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-background text-content-primary">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">出错了</h1>
            <p className="text-content-secondary">请刷新页面重试</p>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

function AppContent() {
  const { fetchUserInfo, isAuthenticated } = useUserStore();
  const { initialize } = useAdminStore();

  // 应用启动时根据路径选择性初始化
  useEffect(() => {
    // 首先尝试从localStorage加载令牌到内存中
    tokenService.loadTokenFromStorage();

    // 根据路径判断应该初始化哪种角色
    const pathname = window.location.pathname;
    const isAdminPath = pathname.startsWith('/admin');
    const isAdminLoginPage = pathname === '/admin/login';

    // 为了防止循环，检查是否有正在进行的重定向
    const isRedirecting = sessionStorage.getItem('admin_redirecting') === 'true';

    if (isRedirecting) {
      console.log('检测到重定向状态，跳过初始化');
      sessionStorage.removeItem('admin_redirecting');
      return;
    }

    // 非管理员路径或首页，只初始化普通用户信息
    if (!isAdminPath || pathname === '/') {
      console.log('初始化普通用户信息');
    fetchUserInfo();
    }

    // 管理员路径且不是登录页，初始化管理员信息
    if (isAdminPath && !isAdminLoginPage) {
      console.log('初始化管理员信息');
    initialize();
      // 管理员页面仍需要基础用户信息，但避免在初始化失败后循环
      const adminStorage = localStorage.getItem('admin-storage');
      if (adminStorage) {
        try {
          const parsed = JSON.parse(adminStorage);
          if (parsed.state && parsed.state.token) {
            fetchUserInfo();
          } else {
            console.log('未找到管理员令牌，跳过用户信息获取');
          }
        } catch {
          console.warn('解析管理员存储失败，跳过用户信息获取');
        }
      } else {
        console.log('未找到管理员存储，跳过用户信息获取');
      }
    }

    // 如果是管理员登录页，不需要初始化任何信息
    if (isAdminLoginPage) {
      console.log('管理员登录页，跳过自动认证');
    }
  }, [fetchUserInfo, initialize]);

  return (
    <Routes>
      {/* 公开路由 */}
      <Route element={<PublicLayout />}>
        <Route path="/" element={
          isAuthenticated ? <Navigate to="/dashboard" replace /> : <LandingPage />
        } />
        <Route path="login" element={<Login />} />
        <Route path="register" element={<Register />} />
        <Route path="verify-email" element={<VerifySuccess />} />
        <Route path="forgot-password" element={<ForgotPassword />} />
        <Route path="reset-password" element={<ResetPassword />} />
      </Route>

      {/* 受保护路由 */}
      <Route element={<PrivateRoute />}>
        <Route element={<AppLayout />}>
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="user-center" element={<UserCenter />} />
          <Route path="subscribe" element={<Subscribe />} />
        </Route>

        {/* 订阅流程页面 */}
        <Route element={<SubscriptionLayout />}>
          <Route path="subscription/success" element={<SubscriptionSuccess />} />
          <Route path="subscription/cancel" element={<Navigate to="/subscribe" replace />} />
        </Route>
      </Route>

      {/* 管理员路由 */}
      <Route path="/admin/login" element={<AdminLogin />} />
      <Route path="/admin" element={<AdminRoute />}>
        <Route path="dashboard" element={<AdminDashboard />} />
        <Route path="users" element={<AdminUsers />} />
        <Route path="announcements" element={<AdminAnnouncements />} />
        <Route path="settings" element={<SettingsIndex />} />
        <Route path="settings/basic" element={<BasicSettings />} />
        <Route path="settings/user" element={<UserSettings />} />
        <Route path="settings/payment" element={<PaymentSettings />} />
        <Route path="settings/security" element={<SecuritySettings />} />
        <Route path="payments" element={<AdminPayments />} />
        <Route path="payments/:id" element={<AdminPaymentDetails />} />
        <Route path="feedback" element={<AdminFeedback />} />
        <Route path="feedback/:id" element={<AdminFeedbackDetail />} />
      </Route>
    </Routes>
  );
}

// 创建一个高阶组件来处理身份验证
const AuthWrapper = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // 使用token刷新hook
  useTokenRefresh();

  // 全局错误处理
  useEffect(() => {
    const handleUnauthorized = (event: CustomEvent) => {
      // 如果当前不在登录页，重定向到登录页
      if (!location.pathname.includes('/login')) {
        navigate('/login', { state: { reason: 'session_expired', from: location.pathname } });
      }
    };

    // 创建自定义事件监听
    window.addEventListener('unauthorized', handleUnauthorized as EventListener);

    return () => {
      window.removeEventListener('unauthorized', handleUnauthorized as EventListener);
    };
  }, [navigate, location]);

  return <>{children}</>;
};

function App() {
  const [isConfigLoaded, setIsConfigLoaded] = useState(false);

  // 加载网站配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        // 获取网站配置
        const config = await configService.getConfig();

        // 更新网站标题
        document.title = config.siteInfo.siteName;

        // 更新网站描述
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
          metaDescription.setAttribute('content', config.siteInfo.siteDescription);
        }

        // 更新网站关键词
        let metaKeywords = document.querySelector('meta[name="keywords"]');
        if (!metaKeywords) {
          metaKeywords = document.createElement('meta');
          metaKeywords.setAttribute('name', 'keywords');
          document.head.appendChild(metaKeywords);
        }
        metaKeywords.setAttribute('content', config.siteInfo.siteKeywords);

        // 更新网站图标
        const favicon = document.querySelector('link[rel="icon"]');
        if (favicon && config.siteInfo.faviconUrl) {
          favicon.setAttribute('href', config.siteInfo.faviconUrl.startsWith('http')
            ? config.siteInfo.faviconUrl
            : `${process.env.REACT_APP_API_URL || ''}${config.siteInfo.faviconUrl}`);
        }

        setIsConfigLoaded(true);
      } catch (error) {
        console.error('加载网站配置失败:', error);
        // 即使配置加载失败，也允许应用继续运行
        setIsConfigLoaded(true);
      }
    };

    loadConfig();
  }, []);

  // 显示加载指示器
  if (!isConfigLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="App">
      <ErrorBoundary>
        <Router>
          <Routes>
            <Route path="/*" element={
              <AuthWrapper>
                <AppContent />
              </AuthWrapper>
            } />
          </Routes>
          <Toaster />
        </Router>
      </ErrorBoundary>
    </div>
  );
}

export default App;
