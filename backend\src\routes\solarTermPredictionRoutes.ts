import { Router } from 'express';
import solarTermPredictionController from '../controllers/solarTermPredictionController';

// 创建路由实例
const router = Router();

// 获取节气预测折线数据路由
// GET /api/solar-term-predictions
router.get('/', solarTermPredictionController.getRecentPredictions);

// 触发节气预测折线生成路由
// POST /api/solar-term-predictions/trigger
router.post('/trigger', solarTermPredictionController.triggerPrediction);

// 触发预测折线完整性检查路由
// POST /api/solar-term-predictions/check-integrity
router.post('/check-integrity', solarTermPredictionController.checkPredictionIntegrity);

export default router; 