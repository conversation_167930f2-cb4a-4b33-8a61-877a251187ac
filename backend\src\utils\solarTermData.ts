/**
 * 节气数据常量文件
 * 
 * 存储预先计算的节气日期信息
 * 该文件应通过自动化脚本定期更新（每年1月1日）
 * 所有日期基于北京时间 (UTC+8)
 * 格式：{year: {month: day}}，表示该年该月第一个节气的日期
 */

// 节气信息接口定义
export interface SolarTermData {
  // 按年份索引
  [year: string]: {
    // 按月份索引(1-12)，值为该月第一个节气的日期(1-31)
    [month: string]: number;
  };
}

/**
 * 月份第一个节气日期表
 * 预先计算并存储，避免运行时计算
 */
export const FIRST_SOLAR_TERMS: SolarTermData = {
  // 2024年每月第一个节气日期
  '2024': {
    '1': 6,  // 小寒 (1月6日)
    '2': 4,  // 立春 (2月4日)
    '3': 5,  // 惊蛰 (3月5日)
    '4': 4,  // 清明 (4月4日)
    '5': 5,  // 立夏 (5月5日)
    '6': 5,  // 芒种 (6月5日)
    '7': 6,  // 小暑 (7月6日)
    '8': 7,  // 立秋 (8月7日)
    '9': 7,  // 白露 (9月7日)
    '10': 8,  // 寒露 (10月8日)
    '11': 7,  // 立冬 (11月7日)
    '12': 7,  // 大雪 (12月7日)
  },

  // 2025年每月第一个节气日期
  '2025': {
    '1': 5,  // 小寒 (1月5日)
    '2': 3,  // 立春 (2月3日)
    '3': 5,  // 惊蛰 (3月5日)
    '4': 4,  // 清明 (4月4日)
    '5': 5,  // 立夏 (5月5日)
    '6': 5,  // 芒种 (6月5日)
    '7': 7,  // 小暑 (7月7日)
    '8': 7,  // 立秋 (8月7日)
    '9': 7,  // 白露 (9月7日)
    '10': 8,  // 寒露 (10月8日)
    '11': 7,  // 立冬 (11月7日)
    '12': 7,  // 大雪 (12月7日)
  }
};

// 节气名称数组（仅供参考）
export const SOLAR_TERM_NAMES = [
  '小寒', '大寒', '立春', '雨水', '惊蛰', '春分',
  '清明', '谷雨', '立夏', '小满', '芒种', '夏至',
  '小暑', '大暑', '立秋', '处暑', '白露', '秋分',
  '寒露', '霜降', '立冬', '小雪', '大雪', '冬至'
]; 