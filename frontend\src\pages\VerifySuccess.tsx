import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams, Link } from 'react-router-dom';
import useUserStore from '../store/useUserStore';

const VerifySuccess: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { verifyEmail, isLoading, error, isInitializing } = useUserStore();
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'success' | 'error'>('pending');
  
  useEffect(() => {
    const token = searchParams.get('token');
    
    if (!token) {
      setVerificationStatus('error');
      return;
    }
    
    const verifyToken = async () => {
      try {
        await verifyEmail(token);
        setVerificationStatus('success');
        
        // 3秒后重定向到登录页
        setTimeout(() => {
          navigate('/login', { 
            state: { message: '邮箱验证成功，请登录' } 
          });
        }, 3000);
      } catch (error) {
        setVerificationStatus('error');
      }
    };
    
    verifyToken();
  }, [searchParams, verifyEmail, navigate]);
  
  if (isInitializing) {
    return <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>;
  }
  
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background">
        <div className="w-full max-w-md bg-white dark:bg-background-card p-8 rounded-lg shadow-card text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-content-muted dark:text-content-secondary">正在验证您的邮箱...</p>
        </div>
      </div>
    );
  }
  
  if (verificationStatus === 'error' || error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background">
        <div className="w-full max-w-md bg-white dark:bg-background-card p-8 rounded-lg shadow-card text-center">
          <div className="text-red-600 text-5xl mb-4">✗</div>
          <h2 className="text-2xl font-bold mb-2 text-gray-800 dark:text-content-primary">验证失败</h2>
          <p className="text-content-muted dark:text-content-secondary mb-6">
            {error || '验证链接无效或已过期'}
          </p>
          <Link 
            to="/login" 
            className="inline-block bg-primary hover:bg-primary-dark text-content-primary py-2 px-6 rounded-md transition-colors duration-200"
          >
            返回登录
          </Link>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background">
      <div className="w-full max-w-md bg-white dark:bg-background-card p-8 rounded-lg shadow-card text-center">
        <div className="text-green-600 text-5xl mb-4">✓</div>
        <h2 className="text-2xl font-bold mb-2 text-gray-800 dark:text-content-primary">邮箱验证成功</h2>
        <p className="text-content-muted dark:text-content-secondary mb-6">您的邮箱已验证成功，现在可以登录使用所有功能。</p>
        <Link 
          to="/login" 
          className="inline-block bg-primary hover:bg-primary-dark text-content-primary py-2 px-6 rounded-md transition-colors duration-200"
        >
          立即登录
        </Link>
        <p className="mt-4 text-sm text-content-muted dark:text-content-secondary">页面将在3秒后自动跳转...</p>
      </div>
    </div>
  );
};

export default VerifySuccess; 