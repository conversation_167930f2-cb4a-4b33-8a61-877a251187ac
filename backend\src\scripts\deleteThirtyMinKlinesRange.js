const mongoose = require('mongoose');

// 直接定义MongoDB连接URI，不再依赖config模块
const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/btc-prediction';

// 定义与ThirtyMinKline模型相同的Schema
const ThirtyMinKlineSchema = new mongoose.Schema({
  symbol: { type: String, required: true, index: true },
  openTime: { type: Number, required: true, index: true },
  closeTime: { type: Number, required: true },
  open: { type: String, required: true },
  high: { type: String, required: true },
  low: { type: String, required: true },
  close: { type: String, required: true },
  volume: { type: String, required: true },
  quoteVolume: { type: String, required: true },
  trades: { type: Number, required: true }
}, {
  timestamps: true,
  versionKey: false
});

// 创建复合索引
ThirtyMinKlineSchema.index({ symbol: 1, openTime: 1 }, { unique: true });

// 创建模型
const ThirtyMinKlineModel = mongoose.model('ThirtyMinKline', ThirtyMinKlineSchema);

/**
 * 删除指定时间范围内的30分钟K线数据脚本
 * 
 * 使用方法:
 * node src/scripts/deleteThirtyMinKlinesRange.js [symbol] [startDate] [endDate]
 * 
 * 参数:
 * symbol - 交易对符号，默认为BTCUSDT
 * startDate - 开始日期，格式为YYYY-MM-DD，默认为30天前
 * endDate - 结束日期，格式为YYYY-MM-DD，默认为当前日期
 * 
 * 示例:
 * node src/scripts/deleteThirtyMinKlinesRange.js BTCUSDT 2023-01-01 2023-01-31 - 删除2023年1月的BTCUSDT 30分钟K线数据
 * node src/scripts/deleteThirtyMinKlinesRange.js BTCUSDT 2023-01-01 - 删除2023年1月1日至今的BTCUSDT 30分钟K线数据
 * node src/scripts/deleteThirtyMinKlinesRange.js - 删除最近30天所有交易对的30分钟K线数据
 */
async function deleteThirtyMinKlinesRange() {
  try {
    // 解析命令行参数
    const args = process.argv.slice(2);
    const symbol = args[0] || 'BTCUSDT'; // 默认为BTCUSDT
    
    // 计算默认日期范围（默认为最近30天）
    const now = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(now.getDate() - 30);
    
    // 解析日期参数，格式为YYYY-MM-DD
    let startDate = args[1] ? new Date(args[1]) : thirtyDaysAgo;
    let endDate = args[2] ? new Date(args[2]) : now;
    
    // 确保日期有效
    if (isNaN(startDate.getTime())) {
      console.error('无效的开始日期格式，请使用YYYY-MM-DD格式');
      process.exit(1);
    }
    
    if (isNaN(endDate.getTime())) {
      console.error('无效的结束日期格式，请使用YYYY-MM-DD格式');
      process.exit(1);
    }
    
    // 设置时间为一天的开始和结束
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(23, 59, 59, 999);
    
    // 转换为时间戳
    const startTimestamp = startDate.getTime();
    const endTimestamp = endDate.getTime();
    
    // 连接数据库
    console.log('正在连接数据库...');
    console.log(`MongoDB URI: ${MONGO_URI}`);
    await mongoose.connect(MONGO_URI);
    console.log('数据库连接成功');

    // 构建查询条件
    const query = {
      symbol,
      openTime: {
        $gte: startTimestamp,
        $lte: endTimestamp
      }
    };

    // 统计删除前的记录数
    const totalCount = await ThirtyMinKlineModel.countDocuments(query);
    console.log(`当前数据库中符合条件的30分钟K线记录共有${totalCount}条`);
    console.log(`查询条件: 交易对=${symbol}, 时间范围=${startDate.toISOString()} 至 ${endDate.toISOString()}`);

    if (totalCount === 0) {
      console.log('没有需要删除的数据');
      await mongoose.connection.close();
      console.log('数据库连接已关闭');
      process.exit(0);
      return;
    }

    // 删除符合条件的30分钟K线数据
    console.log('开始删除时间范围内的30分钟K线数据...');
    const result = await ThirtyMinKlineModel.deleteMany(query);
    
    console.log(`成功删除${result.deletedCount}条30分钟K线记录`);
    console.log(`删除操作${result.acknowledged ? '已确认' : '未确认'}`);
    console.log(`已删除 ${startDate.toLocaleDateString()} 至 ${endDate.toLocaleDateString()} 期间的 ${symbol} 30分钟K线数据`);
    
  } catch (error) {
    console.error('删除K线数据时出错:', error);
  } finally {
    // 关闭数据库连接
    if (mongoose.connection.readyState !== 0) { // 0 = disconnected
      await mongoose.connection.close();
      console.log('数据库连接已关闭');
    }
    process.exit(0);
  }
}

// 执行脚本
deleteThirtyMinKlinesRange(); 