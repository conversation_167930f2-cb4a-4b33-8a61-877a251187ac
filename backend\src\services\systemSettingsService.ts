import SystemSettings, { SystemSettingsDocument } from '../models/SystemSettings';
import mongoose from 'mongoose';
import configService from './configService';
import path from 'path';
import fs from 'fs';
import { promisify } from 'util';

// 文件操作的Promise版本
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

/**
 * 获取系统设置
 * 如果设置不存在，则创建并返回默认设置
 */
export const getSystemSettings = async (): Promise<SystemSettingsDocument> => {
  try {
    // 使用配置服务获取设置
    return await configService.getSettings();
  } catch (error) {
    console.error('获取系统设置时出错:', error);
    throw new Error('获取系统设置失败');
  }
};

/**
 * 更新系统设置
 * @param settings 要更新的设置
 * @param userId 更新人的ID
 */
export const updateSystemSettings = async (
  settings: Partial<SystemSettingsDocument>,
  userId?: string
): Promise<SystemSettingsDocument> => {
  try {
    // 使用配置服务更新设置
    return await configService.updateSettings(settings, userId);
  } catch (error) {
    console.error('更新系统设置时出错:', error);
    throw new Error('更新系统设置失败');
  }
};

/**
 * 保存上传的图片文件
 * @param file 上传的文件Buffer
 * @param fileName 文件名
 * @param fileType 文件类型 (logo 或 favicon)
 * @returns 保存后的文件URL
 */
export const saveImageFile = async (
  file: Buffer,
  fileName: string,
  fileType: 'logo' | 'favicon'
): Promise<string> => {
  try {
    // 确保上传目录存在
    const uploadDir = path.join(process.cwd(), 'public', 'uploads');
    if (!(await existsAsync(uploadDir))) {
      await mkdirAsync(uploadDir, { recursive: true });
    }

    // 生成文件名和路径
    const fileExt = path.extname(fileName);
    const newFileName = `${fileType}_${Date.now()}${fileExt}`;
    const filePath = path.join(uploadDir, newFileName);

    // 保存文件
    await writeFileAsync(filePath, file);

    // 返回相对URL路径
    return `/uploads/${newFileName}`;
  } catch (error) {
    console.error('保存图片文件失败:', error);
    throw new Error('保存图片文件失败');
  }
};

/**
 * 获取订阅价格
 * @param plan 订阅计划
 * @returns 价格（美元）
 */
export const getSubscriptionPrice = async (plan: 'monthly' | 'quarterly' | 'yearly'): Promise<number> => {
  return await configService.get(`subscriptionPrices.${plan}`, 0);
};

/**
 * 获取默认试用天数
 */
export const getDefaultTrialDays = async (): Promise<number> => {
  return await configService.get('userSettings.trialDays', 7);
};

/**
 * 检查是否需要邀请码注册
 */
export const isInviteCodeRequired = async (): Promise<boolean> => {
  return await configService.get('userSettings.inviteCodeRequired', false);
};

/**
 * 获取邀请奖励天数
 */
export const getInviteRewardDays = async (): Promise<number> => {
  return await configService.get('userSettings.inviteRewardDays', 30);
};

/**
 * 检查是否允许注册
 */
export const isRegistrationEnabled = async (): Promise<boolean> => {
  return await configService.get('userSettings.enableRegistration', true);
};

/**
 * 获取默认注册角色
 */
export const getDefaultRole = async (): Promise<'trial' | 'normal'> => {
  return await configService.get('userSettings.defaultRole', 'trial');
};

/**
 * 获取密码最小长度
 */
export const getPasswordMinLength = async (): Promise<number> => {
  return await configService.get('userSettings.passwordMinLength', 6);
};

/**
 * 检查密码是否需要特殊字符
 */
export const isPasswordRequireSpecialChar = async (): Promise<boolean> => {
  return await configService.get('userSettings.passwordRequireSpecialChar', false);
};

/**
 * 获取允许的邮箱域名列表
 */
export const getAllowedEmailDomains = async (): Promise<string[]> => {
  return await configService.get('userSettings.allowedEmailDomains', []);
};

/**
 * 获取网站名称
 */
export const getSiteName = async (): Promise<string> => {
  return await configService.get('siteInfo.siteName', 'BTC 预测');
};

/**
 * 获取网站描述
 */
export const getSiteDescription = async (): Promise<string> => {
  return await configService.get('siteInfo.siteDescription', '');
};

/**
 * 获取默认语言
 */
export const getDefaultLanguage = async (): Promise<'zh' | 'en'> => {
  return await configService.get('siteInfo.defaultLanguage', 'zh');
};

export default {
  getSystemSettings,
  updateSystemSettings,
  saveImageFile,
  getSubscriptionPrice,
  getDefaultTrialDays,
  isInviteCodeRequired,
  getInviteRewardDays,
  isRegistrationEnabled,
  getDefaultRole,
  getPasswordMinLength,
  isPasswordRequireSpecialChar,
  getAllowedEmailDomains,
  getSiteName,
  getSiteDescription,
  getDefaultLanguage
};