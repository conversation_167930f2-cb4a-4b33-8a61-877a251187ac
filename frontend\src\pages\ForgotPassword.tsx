import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import AuthForm from '../components/AuthForm';
import useUserStore from '../store/useUserStore';

const ForgotPassword: React.FC = () => {
  const navigate = useNavigate();
  const { forgotPassword, isLoading, error, isAuthenticated, clearError, isInitializing } = useUserStore();
  const [emailSent, setEmailSent] = useState(false);
  
  // 清除之前的错误
  useEffect(() => {
    clearError();
  }, [clearError]);
  
  // 处理忘记密码表单提交
  const handleSubmit = async (formData: Record<string, string>) => {
    try {
      await forgotPassword(formData.email);
      setEmailSent(true);
    } catch (error) {
      // 错误已在store中处理
      console.error('发送重置密码邮件失败', error);
    }
  };
  
  // 忘记密码表单字段配置
  const fields = [
    {
      name: 'email',
      label: '邮箱',
      type: 'email',
      placeholder: '请输入注册时的邮箱地址'
    }
  ];
  
  // 底部登录链接
  const footerText = (
    <>
      记起密码了？{' '}
      <Link to="/login" className="text-blue-600 hover:underline">
        返回登录
      </Link>
    </>
  );
  
  // 如果正在初始化，显示加载状态
  if (isInitializing) {
    return <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>;
  }
  
  // 如果邮件已发送，显示提示信息
  if (emailSent) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background px-4">
        <div className="w-full max-w-md bg-white dark:bg-background-card p-8 rounded-lg shadow-card text-center">
          <div className="text-green-600 text-5xl mb-4">✓</div>
          <h2 className="text-2xl font-bold mb-2 text-gray-800 dark:text-content-primary">邮件已发送</h2>
          <p className="text-content-muted dark:text-content-secondary mb-4">
            如果该邮箱已注册，我们已向其发送了密码重置链接。请查看您的邮箱并按照邮件中的指示操作。
          </p>
          <p className="text-content-muted dark:text-content-secondary mb-4">
            密码重置链接有效期为1小时。如果您没有收到邮件，请检查垃圾邮件文件夹。
          </p>
          <Link 
            to="/login" 
            className="inline-block bg-primary hover:bg-primary-dark text-content-primary py-2 px-4 rounded-md transition-colors duration-200"
          >
            返回登录
          </Link>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background px-4">
      <div className="w-full max-w-md">
        <AuthForm
          title="忘记密码"
          fields={fields}
          submitText="发送重置链接"
          onSubmit={handleSubmit}
          isLoading={isLoading}
          error={error}
          footerText={footerText}
        />
      </div>
    </div>
  );
};

export default ForgotPassword; 