import nodemailer from 'nodemailer';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret';
// 开发环境下模拟邮件发送
const DEV_MODE = process.env.NODE_ENV !== 'production';

// 邮件发送配置
// 实际项目中应该使用真实的邮件服务配置
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || 'smtp.example.com',
  port: parseInt(process.env.EMAIL_PORT || '587'),
  secure: process.env.EMAIL_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_USER || '<EMAIL>',
    pass: process.env.EMAIL_PASS || 'password'
  }
});

// 存储开发环境下的验证token，用于直接查看
const devVerificationTokens: Record<string, string> = {};

/**
 * 生成邮箱验证 token
 */
export const generateVerificationToken = (userId: string): string => {
  return jwt.sign(
    { userId, purpose: 'email-verification' },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
};

/**
 * 生成密码重置 token
 */
export const generatePasswordResetToken = (userId: string): string => {
  return jwt.sign(
    { userId, purpose: 'password-reset' },
    JWT_SECRET,
    { expiresIn: '1h' }
  );
};

/**
 * 发送验证邮件
 */
export const sendVerificationEmail = async (email: string, userId: string): Promise<boolean> => {
  try {
    // 生成验证 token
    const verificationToken = generateVerificationToken(userId);
    
    // 构建验证链接
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify-email?token=${verificationToken}`;
    
    // 构建邮件内容
    const mailOptions = {
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: email,
      subject: 'BTC预测 - 请验证您的邮箱',
      html: `
        <h2>感谢您注册 BTC 预测平台</h2>
        <p>请点击下面的链接验证您的邮箱地址：</p>
        <a href="${verificationUrl}">验证邮箱</a>
        <p>此链接将在 24 小时后失效。</p>
        <p>如果您没有注册我们的服务，请忽略此邮件。</p>
      `
    };
    
    // 开发环境下只打印邮件内容，不实际发送
    if (DEV_MODE) {
      console.log('\n=========开发环境模拟邮件===========');
      console.log(`发送至: ${email}`);
      console.log(`验证链接: ${verificationUrl}`);
      console.log(`验证Token: ${verificationToken}`);
      console.log('=================================\n');
      
      // 存储验证token，用于后续直接查看
      devVerificationTokens[email] = verificationToken;
      
      return true;
    }
    
    // 实际环境下发送邮件
    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error('发送验证邮件失败:', error);
    return false;
  }
};

/**
 * 发送密码重置邮件
 */
export const sendPasswordResetEmail = async (email: string, userId: string): Promise<boolean> => {
  try {
    // 生成重置密码 token
    const resetToken = generatePasswordResetToken(userId);
    
    // 构建重置密码链接
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}`;
    
    // 构建邮件内容
    const mailOptions = {
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: email,
      subject: 'BTC预测 - 重置您的密码',
      html: `
        <h2>密码重置请求</h2>
        <p>您收到这封邮件是因为您（或其他人）请求重置您的账号密码。</p>
        <p>请点击下面的链接设置新密码：</p>
        <a href="${resetUrl}">重置密码</a>
        <p>此链接将在 1 小时后失效。</p>
        <p>如果您没有请求重置密码，请忽略此邮件，您的密码将保持不变。</p>
      `
    };
    
    // 开发环境下只打印邮件内容，不实际发送
    if (DEV_MODE) {
      console.log('\n=========开发环境模拟邮件===========');
      console.log(`发送至: ${email}`);
      console.log(`重置密码链接: ${resetUrl}`);
      console.log(`重置Token: ${resetToken}`);
      console.log('=================================\n');
      return true;
    }
    
    // 实际环境下发送邮件
    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error('发送密码重置邮件失败:', error);
    return false;
  }
};

/**
 * 验证邮箱验证 token
 */
export const verifyEmailToken = (token: string): { userId: string } | null => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string; purpose: string };
    
    // 验证 token 的用途是否为邮箱验证
    if (decoded.purpose !== 'email-verification') {
      return null;
    }
    
    return { userId: decoded.userId };
  } catch (error) {
    return null;
  }
};

/**
 * 验证密码重置 token
 */
export const verifyPasswordResetToken = (token: string): { userId: string } | null => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string; purpose: string };
    
    // 验证 token 的用途是否为密码重置
    if (decoded.purpose !== 'password-reset') {
      return null;
    }
    
    return { userId: decoded.userId };
  } catch (error) {
    return null;
  }
};

/**
 * 获取开发环境中用户的验证Token (仅供开发使用)
 */
export const getDevVerificationToken = (email: string): string | null => {
  return devVerificationTokens[email] || null;
};

export default sendVerificationEmail; 