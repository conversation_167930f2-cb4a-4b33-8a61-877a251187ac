import React, { useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import AuthForm from '../components/AuthForm';
import useUserStore from '../store/useUserStore';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, isLoading, error, isAuthenticated, clearError, isInitializing } = useUserStore();
  
  // 获取用户之前尝试访问的页面，如果没有则默认为首页
  const from = (location.state as any)?.from || '/';
  
  // 如果用户已经登录，重定向到之前的页面
  useEffect(() => {
    if (isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);
  
  // 清除之前的错误
  useEffect(() => {
    clearError();
  }, [clearError]);
  
  // 处理登录表单提交
  const handleSubmit = async (formData: Record<string, string>) => {
    try {
      await login(formData.email, formData.password);
      // 登录成功，将在useEffect中重定向
    } catch (error) {
      // 错误已在store中处理
      console.error('登录失败', error);
    }
  };
  
  // 登录表单字段配置
  const fields = [
    {
      name: 'email',
      label: '邮箱',
      type: 'email',
      placeholder: '请输入邮箱地址'
    },
    {
      name: 'password',
      label: '密码',
      type: 'password',
      placeholder: '请输入密码'
    }
  ];
  
  // 底部注册链接和忘记密码链接
  const footerText = (
    <div className="flex flex-col space-y-2">
      <div>
        还没有账号？{' '}
        <Link to="/register" className="text-blue-600 hover:underline">
          立即注册
        </Link>
      </div>
      <div>
        <Link to="/forgot-password" className="text-blue-600 hover:underline">
          忘记密码？
        </Link>
      </div>
    </div>
  );
  
  // 显示从其他页面传递来的消息（如密码重置成功）
  const message = (location.state as any)?.message;
  
  // 如果正在初始化，显示加载状态
  if (isInitializing) {
    return <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>;
  }
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background px-4">
      <div className="w-full max-w-md">
        {message && (
          <div className="mb-4 p-3 bg-success/10 border border-success text-green-700 rounded text-center">
            {message}
          </div>
        )}
        <AuthForm
          title="登录"
          fields={fields}
          submitText="登录"
          onSubmit={handleSubmit}
          isLoading={isLoading}
          error={error}
          footerText={footerText}
        />
      </div>
    </div>
  );
};

export default Login; 