import React, { useState, useEffect } from 'react';

interface AnnouncementEditorProps {
  initialData?: {
    _id?: string;
    title: string;
    content: string;
    isVisible: boolean;
    isPinned: boolean;
  };
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  isLoading: boolean;
}

const AnnouncementEditor: React.FC<AnnouncementEditorProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading
}) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [isVisible, setIsVisible] = useState(true);
  const [isPinned, setIsPinned] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (initialData) {
      setTitle(initialData.title);
      setContent(initialData.content);
      setIsVisible(initialData.isVisible);
      setIsPinned(initialData.isPinned);
    }
  }, [initialData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!title.trim()) {
      setError('请输入公告标题');
      return;
    }

    if (!content.trim()) {
      setError('请输入公告内容');
      return;
    }

    try {
      await onSubmit({
        ...(initialData?._id && { _id: initialData._id }),
        title,
        content,
        isVisible,
        isPinned
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存公告失败');
    }
  };

  return (
    <div className="bg-white dark:bg-background-card rounded-lg shadow-card p-6">
      <h2 className="text-xl font-semibold text-gray-800 dark:text-content-primary mb-4">
        {initialData?._id ? '编辑公告' : '创建公告'}
      </h2>

      {error && (
        <div className="bg-error/10 border-l-4 border-red-500 text-red-700 p-4 mb-4">
          <p>{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label
            htmlFor="title"
            className="block text-sm font-medium text-gray-700 dark:text-content-secondary mb-1"
          >
            公告标题
          </label>
          <input
            type="text"
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-background-input dark:border-border dark:text-content-primary"
            placeholder="输入公告标题"
          />
        </div>

        <div className="mb-4">
          <label
            htmlFor="content"
            className="block text-sm font-medium text-gray-700 dark:text-content-secondary mb-1"
          >
            公告内容
          </label>
          <textarea
            id="content"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            rows={6}
            className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-background-input dark:border-border dark:text-content-primary"
            placeholder="输入公告内容"
          ></textarea>
        </div>

        <div className="mb-4 flex items-center space-x-6">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isVisible"
              checked={isVisible}
              onChange={(e) => setIsVisible(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-border rounded"
            />
            <label
              htmlFor="isVisible"
              className="ml-2 block text-sm text-gray-700 dark:text-content-secondary"
            >
              显示公告
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="isPinned"
              checked={isPinned}
              onChange={(e) => setIsPinned(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-border rounded"
            />
            <label
              htmlFor="isPinned"
              className="ml-2 block text-sm text-gray-700 dark:text-content-secondary"
            >
              置顶公告
            </label>
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-border text-gray-700 rounded-md hover:bg-gray-50 dark:border-border dark:text-content-secondary dark:hover:bg-white/5 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            取消
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="px-4 py-2 bg-blue-500 text-content-primary rounded-md hover:bg-primary focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-400 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <span className="flex items-center">
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-content-primary"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                保存中...
              </span>
            ) : (
              '保存公告'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AnnouncementEditor; 