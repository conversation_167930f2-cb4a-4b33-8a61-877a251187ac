/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // 主色调
        primary: {
          DEFAULT: '#3B82F6',
          light: '#60A5FA',
          dark: '#2563EB',
          foreground: '#FFFFFF',
        },
        // 次要色调
        secondary: {
          DEFAULT: '#475569',
          light: '#64748B',
          dark: '#334155',
          foreground: '#FFFFFF',
        },
        // 背景色
        background: {
          DEFAULT: '#171717',
          light: '#1d1e1f',
          card: '#1d1e1f',//#1d1e1f 1E293B
          input: '#1A1F2E',
        },
        // 文本色
        content: {
          primary: '#FFFFFF',
          secondary: '#94A3B8',
          muted: '#64748B',
        },
        // 强调色
        accent: {
          DEFAULT: '#3B82F6',/////
          light: '#A78BFA',//
          dark: '#3B82F6',////
          foreground: '#FFFFFF',
        },
        // 功能色
        success: '#10B981',
        warning: '#F59E0B',
        error: '#EF4444',
        info: '#3B82F6',
        // 边框色
        border: {
          DEFAULT: 'rgba(255, 255, 255, 0.1)',
          light: 'rgba(255, 255, 255, 0.05)',
          strong: 'rgba(255, 255, 255, 0.2)',
        },
        // toast 相关颜色
        foreground: '#FFFFFF',
        destructive: {
          DEFAULT: "#EF4444",
          foreground: "#FFFFFF",
        },
        muted: {
          DEFAULT: "#404040",
          foreground: "#A3A3A3",
        },
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace'],
      },
      boxShadow: {
        'glow': '0 0 15px rgba(59, 130, 246, 0.5)',
        'glow-lg': '0 0 30px rgba(59, 130, 246, 0.5)',
        'card': '0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06)',
      },
      animation: {
        'gradient': 'gradient 8s linear infinite',
        'in': 'in 0.2s ease-out',
        'out': 'out 0.2s ease-in',
        'fade-in': 'fade-in 0.2s ease-in',
        'fade-out': 'fade-out 0.2s ease-out',
        'slide-in-from-top-full': 'slide-in-from-top-full 0.3s ease-out',
        'slide-in-from-bottom-full': 'slide-in-from-bottom-full 0.3s ease-out',
        'slide-out-to-right-full': 'slide-out-to-right-full 0.3s ease-in',
      },
      keyframes: {
        gradient: {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'left center'
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'right center'
          },
        },
        'in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'out': {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'fade-out': {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        'slide-in-from-top-full': {
          '0%': { transform: 'translateY(-100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        'slide-in-from-bottom-full': {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        'slide-out-to-right-full': {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(100%)' },
        },
      },
    },
  },
  plugins: [
    require('tailwindcss-animate'),
  ],
} 