const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');

// 加载环境变量
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// 获取MongoDB连接URI
const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/btc-prediction';

// 预测模型定义 - 确保与实际项目中定义完全一致
const predictionSchema = new mongoose.Schema({
  symbol: { type: String, required: true, index: true },
  predictionTime: { type: Number, required: true },
  targetStartTime: { type: Number, required: true, index: true },
  targetEndTime: { type: Number, required: true },
  sourceKlineTime: { type: Number, required: true },
  open: { type: String, required: true },
  high: { type: String, required: true },
  low: { type: String, required: true },
  close: { type: String, required: true },
  isActive: { type: <PERSON>olean, default: true },
  createdAt: { type: Date, default: Date.now }
}, {
  timestamps: true,
  versionKey: false
});

// 添加与项目中相同的索引
predictionSchema.index({ symbol: 1, targetStartTime: 1 }, { unique: true });

/**
 * 清空预测数据库脚本
 * 删除所有历史预测数据
 */
async function clearPredictions() {
  try {
    // 连接数据库
    console.log('正在连接数据库...');
    console.log(`MongoDB URI: ${MONGO_URI}`);
    await mongoose.connect(MONGO_URI);
    console.log('数据库连接成功');

    // 使用正确的集合名称
    const collection = mongoose.connection.collection('predictions');
    console.log(`连接到集合: ${collection.collectionName}`);

    // 删除所有预测数据
    console.log('开始删除所有预测数据...');
    const result = await collection.deleteMany({});
    
    console.log(`成功删除${result.deletedCount}条预测记录`);
    console.log('历史预测数据已全部清除');
  } catch (error) {
    console.error('删除预测数据时出错:', error);
  } finally {
    // 关闭数据库连接
    await mongoose.connection.close();
    console.log('数据库连接已关闭');
    process.exit(0);
  }
}

// 执行清除操作
clearPredictions(); 