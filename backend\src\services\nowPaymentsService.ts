import axios from 'axios';
import { v4 as uuidv4 } from 'uuid'; // 需要安装: npm install uuid @types/uuid
import configService from './configService';

// 创建HTTP客户端
// 注意：这里使用了一个函数来创建客户端，以便每次调用时都能获取最新的配置
const createClient = async () => {
  // 从配置服务获取API配置
  const apiBaseUrl = await configService.get('paymentSettings.apiBaseUrl', 'https://api-sandbox.nowpayments.io/v1');
  const apiKey = await configService.get('paymentSettings.apiKey', process.env.NOWPAYMENTS_API_KEY || '');

  // 确保API密钥存在
  if (!apiKey) {
    console.warn('警告: NOWPayments API密钥未设置');
  }

  // 创建并返回HTTP客户端
  return axios.create({
    baseURL: apiBaseUrl,
    headers: {
      'x-api-key': apiKey,
      'Content-Type': 'application/json',
    },
  });
};

// 计算订阅时长（天数）
const getPlanDuration = (plan: string): number => {
  switch (plan) {
    case 'monthly':
      return 30;
    case 'quarterly':
      return 90;
    case 'yearly':
      return 365;
    default:
      return 0;
  }
};

// 获取订阅计划价格（美元）- 硬编码版本，作为备用
const getPlanPrice = (plan: string): number => {
  switch (plan) {
    case 'monthly':
      return 30;
    case 'quarterly':
      return 75;
    case 'yearly':
      return 266;
    default:
      return 0;
  }
};

// 从配置服务获取订阅计划价格
const getConfiguredPlanPrice = async (plan: string): Promise<number> => {
  // 从配置服务获取价格
  const price = await configService.get(`subscriptionPrices.${plan}`, 0);

  // 如果配置中没有价格，使用硬编码的备用价格
  if (price <= 0) {
    return getPlanPrice(plan);
  }

  return price;
};

// 创建支付参数接口
interface CreatePaymentParams {
  price_amount: number;   // 价格金额
  price_currency: string; // 价格货币（例如，USD）
  pay_currency?: string;  // 支付货币（例如，BTC）
  ipn_callback_url?: string; // 回调URL
  order_id: string;       // 订单ID（我们系统中的用户ID+时间戳）
  order_description: string; // 订单描述
  success_url?: string;   // 支付成功跳转URL
  cancel_url?: string;    // 支付取消跳转URL
}

// 原始API返回接口
interface PaymentResponse {
  payment_id?: string;     // 支付ID（可能为undefined）
  id?: string;             // 发票ID
  token_id?: string;       // 令牌ID
  payment_status?: string; // 支付状态
  pay_address?: string;    // 支付地址
  price_amount: number;   // 价格金额
  price_currency: string; // 价格货币
  pay_amount?: number;     // 支付金额
  pay_currency: string;   // 支付货币
  order_id: string;       // 订单ID
  order_description: string; // 订单描述
  ipn_callback_url: string; // 回调URL
  invoice_url: string;    // 发票URL（用户支付页面）
  created_at: string;     // 创建时间
  updated_at?: string;     // 更新时间
  expiration_at?: string;  // 过期时间
}

// 标准化后的响应接口
interface NormalizedPaymentResponse {
  payment_id: string;       // 支付ID (保证不为空)
  invoice_id: string;       // 发票ID
  invoice_url: string;      // 发票URL (支付页面)
  order_id: string;         // 订单ID
  price_amount: number;     // 金额
  price_currency: string;   // 价格货币
  pay_currency: string;     // 支付货币
  pay_address?: string;     // 支付地址
  status: string;           // 状态
  created_at: string;       // 创建时间
  expiration_at?: string;   // 过期时间
}

// 创建支付请求
export const createPayment = async (
  userId: string,
  plan: string,
  callbackUrl: string,
  successUrl: string,
  cancelUrl: string
): Promise<NormalizedPaymentResponse> => {
  try {
    // 获取价格，优先从配置服务获取
    const price = await getConfiguredPlanPrice(plan);
    if (price <= 0) {
      throw new Error('无效的订阅计划');
    }

    // 订单ID使用用户ID和时间戳确保唯一性
    const orderId = `order_${userId}_${plan}_${Date.now()}`;
    // 订单描述
    const description = `${plan} 订阅计划 (${getPlanDuration(plan)}天)`;

    // 从配置服务获取支付币种
    const payCurrency = await configService.get('paymentSettings.payCurrency', 'usdttrc20');

    const paymentData: CreatePaymentParams = {
      price_amount: price,
      price_currency: 'USD',
      pay_currency: payCurrency, // 从配置获取支付币种
      order_id: orderId,
      order_description: description,
      ipn_callback_url: callbackUrl,
      success_url: successUrl,
      cancel_url: cancelUrl
    };

    // 创建HTTP客户端
    const client = await createClient();

    // 调用/invoice接口
    const response = await client.post('/invoice', paymentData);
    const invoiceResponse: PaymentResponse = response.data;

    // 检查invoice_url是否存在
    if (!invoiceResponse.invoice_url) {
      throw new Error('创建发票失败: 缺少invoice_url');
    }

    // 确保payment_id存在，如果不存在则使用其他ID或生成一个
    const paymentId = invoiceResponse.payment_id ||
                     invoiceResponse.id ||
                     invoiceResponse.token_id ||
                     `payment_${uuidv4()}`;

    // 从invoice_url中提取invoice ID (如果有)
    let invoiceId = '';
    try {
      const urlParts = invoiceResponse.invoice_url.split('/');
      invoiceId = urlParts[urlParts.length - 1] || paymentId;
    } catch (e) {
      // 如果无法从URL提取，就使用payment_id
      invoiceId = paymentId;
    }

    // 返回标准化的响应结构
    return {
      payment_id: paymentId,
      invoice_id: invoiceId,
      invoice_url: invoiceResponse.invoice_url,
      order_id: invoiceResponse.order_id,
      price_amount: invoiceResponse.price_amount,
      price_currency: invoiceResponse.price_currency,
      pay_currency: invoiceResponse.pay_currency,
      pay_address: invoiceResponse.pay_address,
      status: invoiceResponse.payment_status || 'pending',
      created_at: invoiceResponse.created_at,
      expiration_at: invoiceResponse.expiration_at
    };
  } catch (error) {
    console.error('NOWPayments API错误:', error);
    if (axios.isAxiosError(error)) {
      throw new Error(`支付创建失败: ${error.response?.data?.message || error.message}`);
    }
    throw error;
  }
};

// 获取支付状态
export const getPaymentStatus = async (paymentId: string): Promise<any> => {
  try {
    // 创建HTTP客户端
    const client = await createClient();

    const response = await client.get(`/payment/${paymentId}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(`获取支付状态失败: ${error.response?.data?.message || error.message}`);
    }
    throw error;
  }
};



export default {
  createPayment,
  getPaymentStatus,
  getPlanDuration,
  getPlanPrice
};