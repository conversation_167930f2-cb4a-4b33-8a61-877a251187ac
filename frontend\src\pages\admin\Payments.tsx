import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import AdminLayout from '../../components/admin/AdminLayout';
import { useToast } from '../../components/ui/use-toast';
import useAdminStore from '../../store/useAdminStore';

// 支付状态标签颜色映射
const statusColors: Record<string, string> = {
  pending: 'bg-yellow-100 text-yellow-800',
  confirming: 'bg-blue-100 text-blue-800',
  confirmed: 'bg-green-100 text-green-800',
  sending: 'bg-purple-100 text-purple-800',
  partially_paid: 'bg-orange-100 text-orange-800',
  finished: 'bg-green-500 text-white',
  failed: 'bg-red-100 text-red-800',
  refunded: 'bg-gray-100 text-gray-800',
  expired: 'bg-red-50 text-red-600'
};

// 支付状态中文描述
const statusLabels: Record<string, string> = {
  pending: '等待支付',
  confirming: '确认中',
  confirmed: '已确认',
  sending: '发送中',
  partially_paid: '部分支付',
  finished: '已完成',
  failed: '失败',
  refunded: '已退款',
  expired: '已过期'
};

// 计划名称中文描述
const planLabels: Record<string, string> = {
  monthly: '月度套餐',
  quarterly: '季度套餐',
  yearly: '年度套餐'
};

const AdminPayments: React.FC = () => {
  const { getPayments, getPaymentStats } = useAdminStore();
  const { toast } = useToast();
  
  // 支付记录状态
  const [payments, setPayments] = useState<any[]>([]);
  const [stats, setStats] = useState<any>({
    totalPayments: 0,
    successfulPayments: 0,
    pendingPayments: 0,
    failedPayments: 0,
    totalRevenue: 0
  });
  
  // 加载状态
  const [isLoading, setIsLoading] = useState(true);
  const [isStatsLoading, setIsStatsLoading] = useState(true);
  
  // 筛选条件
  const [search, setSearch] = useState('');
  const [filters, setFilters] = useState({
    status: '',
    plan: '',
    startDate: '',
    endDate: ''
  });
  
  // 分页
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 1
  });

  // 获取支付记录
  const fetchPayments = async () => {
    try {
      setIsLoading(true);
      const response = await getPayments(
        pagination.page,
        pagination.limit,
        search,
        filters
      );
      
      setPayments(response.payments);
      setPagination(response.pagination);
      setIsLoading(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取支付记录失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };

  // 获取支付统计数据
  const fetchPaymentStats = async () => {
    try {
      setIsStatsLoading(true);
      const response = await getPaymentStats();
      setStats(response.stats);
      setIsStatsLoading(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取支付统计数据失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsStatsLoading(false);
    }
  };

  // 初次加载和筛选条件变化时获取数据
  useEffect(() => {
    fetchPayments();
  }, [pagination.page, filters]); // eslint-disable-line react-hooks/exhaustive-deps

  // 初次加载时获取统计数据
  useEffect(() => {
    fetchPaymentStats();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理搜索
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 })); // 重置到第一页
    fetchPayments();
  };

  // 处理筛选
  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
    setPagination(prev => ({ ...prev, page: 1 })); // 重置到第一页
  };

  // 分页处理
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > pagination.pages) return;
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  // 格式化金额
  const formatAmount = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <AdminLayout>
      <div className="p-6">

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-white dark:bg-background-card shadow-card rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-500 dark:text-content-secondary mb-1">总支付数</h3>
            {isStatsLoading ? (
              <div className="animate-pulse h-6 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
            ) : (
              <p className="text-2xl font-bold text-gray-800 dark:text-content-primary">{stats.totalPayments}</p>
            )}
          </div>
          
          <div className="bg-white dark:bg-background-card shadow-card rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-500 dark:text-content-secondary mb-1">成功支付</h3>
            {isStatsLoading ? (
              <div className="animate-pulse h-6 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
            ) : (
              <p className="text-2xl font-bold text-green-600">{stats.successfulPayments}</p>
            )}
          </div>
          
          <div className="bg-white dark:bg-background-card shadow-card rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-500 dark:text-content-secondary mb-1">待处理支付</h3>
            {isStatsLoading ? (
              <div className="animate-pulse h-6 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
            ) : (
              <p className="text-2xl font-bold text-yellow-600">{stats.pendingPayments}</p>
            )}
          </div>
          
          <div className="bg-white dark:bg-background-card shadow-card rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-500 dark:text-content-secondary mb-1">总收入</h3>
            {isStatsLoading ? (
              <div className="animate-pulse h-6 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
            ) : (
              <p className="text-2xl font-bold text-blue-600">{formatAmount(stats.totalRevenue)}</p>
            )}
          </div>
        </div>

        <div className="bg-white dark:bg-background-card shadow-card rounded-lg p-6 mb-6">
          <div className="md:flex md:justify-between mb-4">
            {/* 搜索框 */}
            <form onSubmit={handleSearch} className="mb-4 md:mb-0">
              <div className="flex">
                <input
                  type="text"
                  placeholder="搜索用户邮箱..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="px-4 py-2 border rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-background-input dark:border-border dark:text-content-primary"
                />
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-500 text-white rounded-r-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  搜索
                </button>
              </div>
            </form>

            {/* 筛选器 */}
            <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4">
              {/* 状态筛选 */}
              <select
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                className="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-background-input dark:border-border dark:text-content-primary"
              >
                <option value="">所有状态</option>
                <option value="pending">等待支付</option>
                <option value="confirming">确认中</option>
                <option value="confirmed">已确认</option>
                <option value="sending">发送中</option>
                <option value="partially_paid">部分支付</option>
                <option value="finished">已完成</option>
                <option value="failed">失败</option>
                <option value="refunded">已退款</option>
                <option value="expired">已过期</option>
              </select>

              {/* 计划筛选 */}
              <select
                name="plan"
                value={filters.plan}
                onChange={handleFilterChange}
                className="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-background-input dark:border-border dark:text-content-primary"
              >
                <option value="">所有套餐</option>
                <option value="monthly">月度套餐</option>
                <option value="quarterly">季度套餐</option>
                <option value="yearly">年度套餐</option>
              </select>
            </div>
          </div>

          {/* 日期范围筛选 */}
          <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4 mb-4">
            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 dark:text-content-secondary mb-1">
                开始日期
              </label>
              <input
                type="date"
                id="startDate"
                name="startDate"
                value={filters.startDate}
                onChange={handleFilterChange}
                className="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-background-input dark:border-border dark:text-content-primary"
              />
            </div>
            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 dark:text-content-secondary mb-1">
                结束日期
              </label>
              <input
                type="date"
                id="endDate"
                name="endDate"
                value={filters.endDate}
                onChange={handleFilterChange}
                className="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-background-input dark:border-border dark:text-content-primary"
              />
            </div>
            <button
              onClick={() => {
                setFilters(prev => ({ ...prev, startDate: '', endDate: '' }));
                setPagination(prev => ({ ...prev, page: 1 }));
                fetchPayments();
              }}
              className="mt-7 px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
            >
              清除日期
            </button>
          </div>

          {/* 支付记录表格 */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-background-input">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-content-muted dark:text-content-secondary uppercase tracking-wider">
                    用户邮箱
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-content-muted dark:text-content-secondary uppercase tracking-wider">
                    金额
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-content-muted dark:text-content-secondary uppercase tracking-wider">
                    套餐
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-content-muted dark:text-content-secondary uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-content-muted dark:text-content-secondary uppercase tracking-wider">
                    创建时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-content-muted dark:text-content-secondary uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-background-card divide-y divide-gray-200 dark:divide-gray-700">
                {isLoading ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center">
                      <div className="flex justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
                      </div>
                    </td>
                  </tr>
                ) : payments.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-content-muted dark:text-content-secondary">
                      没有找到支付记录
                    </td>
                  </tr>
                ) : (
                  payments.map((payment) => (
                    <tr key={payment._id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-content-primary">
                        {payment.userEmail}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-content-primary">
                        {formatAmount(payment.amount)} {payment.currency}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                          {planLabels[payment.plan] || payment.plan}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColors[payment.paymentStatus] || 'bg-gray-100 text-gray-800'}`}>
                          {statusLabels[payment.paymentStatus] || payment.paymentStatus}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-content-secondary">
                        {formatDate(payment.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Link 
                          to={`/admin/payments/${payment._id}`}
                          className="text-blue-600 hover:text-blue-900 dark:text-primary dark:hover:text-blue-300"
                        >
                          详情
                        </Link>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* 分页控制 */}
          {!isLoading && pagination.pages > 1 && (
            <div className="flex justify-center mt-6">
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-border bg-white dark:bg-background-input dark:border-border text-sm font-medium text-content-muted dark:text-content-secondary hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  上一页
                </button>
                {Array.from({ length: pagination.pages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`relative inline-flex items-center px-4 py-2 border border-border dark:border-border text-sm font-medium ${
                      page === pagination.page
                        ? 'z-10 bg-blue-50 border-primary text-blue-600 dark:bg-blue-900 dark:text-blue-200'
                        : 'bg-white dark:bg-background-input text-content-muted dark:text-content-secondary hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    {page}
                  </button>
                ))}
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === pagination.pages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-border bg-white dark:bg-background-input dark:border-border text-sm font-medium text-content-muted dark:text-content-secondary hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  下一页
                </button>
              </nav>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminPayments; 