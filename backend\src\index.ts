import express from 'express';
import mongoose from 'mongoose';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import path from 'path';
import fs from 'fs';
import config from './config';
import routes from './routes';
import scheduler from './utils/scheduler';
import predictionService from './services/predictionService';
import Prediction from './models/Prediction';
import configService from './services/configService';

/**
 * BTC预测系统后端主入口文件
 * 负责初始化Express应用，连接数据库，启动API服务和定时任务
 */

// 创建Express应用实例
const app = express();

// 配置中间件
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',  // 明确指定前端URL
  credentials: true            // 允许跨域请求携带凭证
}));
app.use(express.json());                         // 处理JSON请求体
app.use(express.urlencoded({ extended: true })); // 处理表单数据
app.use(cookieParser());                         // 解析Cookie

// 配置静态文件服务
const publicPath = path.join(__dirname, '..', 'public');
app.use(express.static(publicPath));
console.log('静态文件目录:', publicPath);

// 注册API路由
app.use('/api', routes);

/**
 * 连接MongoDB数据库
 * 使用配置文件中的URI连接MongoDB
 */
const connectDB = async () => {
  try {
    await mongoose.connect(config.mongoUri);
    console.log('MongoDB连接成功');

    // 初始化数据库，清理旧索引
    await initializeDatabase();
  } catch (error) {
    console.error('MongoDB连接错误:', error);
    process.exit(1); // 数据库连接失败时退出应用
  }
};

/**
 * 初始化数据库
 * 清理不再使用的旧索引，确保数据结构正确
 */
async function initializeDatabase() {
  try {
    // 获取Prediction集合
    const collection = mongoose.connection.collection('predictions');

    // 获取所有索引
    const indexes = await collection.indexes();
    console.log('当前索引列表:', indexes);

    // 查找并删除旧的sourceTimestamp/targetTimestamp索引
    for (const index of indexes) {
      if (index.name && (
          index.name.includes('sourceTimestamp') ||
          index.name.includes('targetTimestamp')
      )) {
        console.log(`删除旧索引: ${index.name}`);
        await collection.dropIndex(index.name);
      }
    }

    // 确保targetStartTime索引存在
    const existingIndexes = await collection.indexes();
    const hasTargetStartIndex = existingIndexes.some(idx =>
      idx.name && idx.name.includes('targetStartTime')
    );

    if (!hasTargetStartIndex) {
      console.log('创建symbol和targetStartTime的复合索引');
      await collection.createIndex(
        { symbol: 1, targetStartTime: 1 },
        { unique: true }
      );
    }

    console.log('数据库初始化完成');
  } catch (error) {
    console.error('初始化数据库时出错:', error);
  }
}

/**
 * 启动服务器
 * 连接数据库、启动定时任务并监听端口
 */
const startServer = async () => {
  // 连接数据库
  await connectDB();

  // 初始化配置服务
  try {
    console.log('初始化配置服务...');
    await configService.init();
    console.log('配置服务初始化成功');
  } catch (error) {
    console.error('初始化配置服务出错:', error);
    // 继续启动服务，不因为配置服务初始化失败而中断启动流程
  }

  // 服务器启动时检查并补齐预测K线
  try {
    console.log('服务器启动时检查预测K线数据完整性...');
    const filledPredictions = await predictionService.checkAndFillMissingPredictions();
    console.log(`启动检查完成，已补齐${filledPredictions.length}条预测记录`);
  } catch (error) {
    console.error('启动时检查预测数据出错:', error);
    // 继续启动服务，不因为检查失败而中断启动流程
  }

  // 服务器启动时检查并补齐30分钟K线数据
  try {
    console.log('服务器启动时检查30分钟K线数据完整性...');
    const thirtyMinKlineService = (await import('./services/thirtyMinKlineService')).default;
    const fixedCount = await thirtyMinKlineService.checkAndFillHistoricalData();
    console.log(`30分钟K线数据检查完成，已补齐${fixedCount}条记录`);
  } catch (error) {
    console.error('启动时检查30分钟K线数据出错:', error);
    // 继续启动服务，不因为检查失败而中断启动流程
  }

  // 确保上传目录存在
  const uploadDir = path.join(__dirname, '..', 'public', 'uploads');
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
    console.log('创建上传目录:', uploadDir);
  }

  // 启动定时任务
  scheduler.startAllJobs();

  // 监听端口
  app.listen(config.port, () => {
    console.log(`服务器运行在端口 ${config.port}`);
  });
};

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
});

// 处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝，原因:', reason, '，Promise:', promise);
});

// 启动应用
startServer();