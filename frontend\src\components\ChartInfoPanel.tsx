/**
 * 图表价格信息面板组件
 * 
 * 该组件用于显示K线数据的详细信息，
 * 从Chart.tsx中提取出来，减少主图表组件的复杂度
 */

import React, { useEffect, useRef, useState } from 'react';
import { KLineData } from '../types/chartTypes';
import { formatPriceInfo } from '../utils/chartUtils';
import timezoneManager, { TimezoneOption } from '../utils/timezoneManager';

interface ChartInfoPanelProps {
  latestCandle: KLineData | null;
  hoveredCandle: KLineData | null;
  className?: string;
}

/**
 * 图表价格信息面板组件
 * 显示K线价格信息
 */
const ChartInfoPanel: React.FC<ChartInfoPanelProps> = ({
  latestCandle,
  hoveredCandle,
  className = "absolute top-10 left-1 z-20 bg-opacity-90 p-2 rounded text-sm text-gray-100"
}) => {
  // 引用
  const infoPanelRef = useRef<HTMLDivElement>(null);
  // 时区状态
  const [currentTimezone, setCurrentTimezone] = useState(timezoneManager.getCurrentTimezone());
  
  // 监听时区变更
  useEffect(() => {
    // 时区变更处理函数
    const handleTimezoneChange = (timezone: TimezoneOption) => {
      // 更新当前时区状态，触发重新渲染
      setCurrentTimezone(timezone);
      
      // 立即强制更新价格信息面板
      updatePriceInfo();
    };
    
    // 注册时区变更监听
    timezoneManager.addListener(handleTimezoneChange);
    
    // 组件卸载时移除监听
    return () => timezoneManager.removeListener(handleTimezoneChange);
  }, []);
  
  // 更新价格信息函数
  const updatePriceInfo = () => {
    if (!infoPanelRef.current) return;
    
    // 优先显示鼠标悬停的K线信息，其次显示最新K线信息
    const displayData = hoveredCandle || latestCandle;
    
    if (displayData) {
      infoPanelRef.current.innerHTML = formatPriceInfo(displayData);
    }
  };
  
  // 更新价格信息
  useEffect(() => {
    updatePriceInfo();
  }, [latestCandle, hoveredCandle, currentTimezone]);

  // 渲染价格信息面板
  return (
    <div ref={infoPanelRef} className={className} />
  );
};

export default ChartInfoPanel; 