import axios from 'axios';
import tokenService from './tokenService';
import api from './config'; // 使用统一的axios实例

// 注册新用户
export const register = async (email: string, password: string, inviteCode?: string) => {
  try {
    const response = await api.post('/auth/register', {
      email,
      password,
      inviteCode
    });

    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '注册失败，请稍后重试');
    }
    throw error;
  }
};

// 用户登录
export const login = async (email: string, password: string) => {
  try {
    const response = await api.post('/auth/login', {
      email,
      password
    });

    if (response.data.success && response.data.accessToken) {
      // 新API返回accessToken，为了兼容也返回token字段
      const accessToken = response.data.accessToken;

      // 将访问令牌存储在内存中
      tokenService.setToken(accessToken);

      // 保持与之前返回结构一致，但只在localStorage中存储token字段
      return {
        ...response.data,
        token: accessToken // 确保返回token字段以兼容现有代码
      };
    }

    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '登录失败，请稍后重试');
    }
    throw error;
  }
};

// 验证邮箱
export const verifyEmail = async (token: string) => {
  try {
    const response = await api.post('/auth/verify-email', { token });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '邮箱验证失败');
    }
    throw error;
  }
};

// 忘记密码 - 发送重置密码邮件
export const forgotPassword = async (email: string) => {
  try {
    const response = await api.post('/auth/forgot-password', { email });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '发送重置密码邮件失败');
    }
    throw error;
  }
};

// 重置密码
export const resetPassword = async (token: string, newPassword: string) => {
  try {
    const response = await api.post('/auth/reset-password', { token, newPassword });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '重置密码失败');
    }
    throw error;
  }
};

// 修改密码（需要认证）
export const changePassword = async (oldPassword: string, newPassword: string) => {
  try {
    const response = await api.post('/auth/change-password', { oldPassword, newPassword });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '修改密码失败');
    }
    throw error;
  }
};

// 获取当前用户信息
export const getMe = async () => {
  try {
    const response = await api.get('/auth/me');
    return response.data.user;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取用户信息失败');
    }
    throw error;
  }
};

// 刷新访问令牌
export const refreshAccessToken = async () => {
  try {
    console.log('调用刷新令牌API...');
    const response = await api.post('/auth/refresh', {}, {
      withCredentials: true
    });

    if (response.data.success && response.data.accessToken) {
      console.log('成功获取新的访问令牌');
      const newToken = response.data.accessToken;
      tokenService.setToken(newToken);
      tokenService.updateStorageToken(newToken);
      return newToken;
    } else {
      console.warn('刷新令牌API返回了成功状态，但没有令牌');
      return null;
    }
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      console.error(`刷新令牌失败: ${error.response.status} - ${error.response.data?.message || '未知错误'}`);
      // 如果收到401状态码，说明refreshToken已过期
      if (error.response.status === 401) {
        throw new Error('session_expired');
      }
    } else {
      console.error('刷新令牌过程中出现错误:', error);
    }
    throw error; // 继续抛出异常以便调用者处理
  }
};

// 注意：响应拦截器已在 config.ts 的统一axios实例中处理，这里不需要重复添加

// 获取认证头
export const getAuthHeader = () => {
  const token = tokenService.getToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
};

export default {
  register,
  login,
  verifyEmail,
  forgotPassword,
  resetPassword,
  changePassword,
  getMe,
  refreshAccessToken,
  getAuthHeader
};