# 后端脚本工具

## 重复奖励记录清理脚本 (cleanDuplicateRewards.js)

此脚本用于清理数据库中重复的邀请奖励记录，保留最早的一条记录。

### 使用方法

```bash
node cleanDuplicateRewards.js
```

### 功能说明

- 查找所有有邀请奖励记录的用户
- 对于有多条邀请奖励记录的用户，保留最早的一条，删除其他重复记录
- 显示清理过程和结果统计

### 注意事项

- 执行前会显示将要处理的用户数量
- 脚本会自动备份要删除的记录信息到控制台
- 需要确保MongoDB连接配置正确（从.env文件读取）

## 预测折线数据清理脚本 (cleanPredictionData.js)

此脚本用于清理数据库中的预测折线数据，支持多种过滤条件。

### 使用方法

```bash
node cleanPredictionData.js [选项]
```

### 选项

- `--all` - 删除所有预测折线数据
- `--symbol=XXX` - 指定要删除的交易对数据，例如: `--symbol=BTCUSDT`
- `--before=XXX` - 删除指定日期之前的数据，格式: YYYY-MM-DD，例如: `--before=2024-05-01`
- `--after=XXX` - 删除指定日期之后的数据，格式: YYYY-MM-DD，例如: `--after=2024-01-01`
- `--inactive` - 只删除非活跃预测数据
- `--help` - 显示帮助信息

### 使用示例

1. 显示帮助信息:
```bash
node cleanPredictionData.js --help
```

2. 删除所有预测数据:
```bash
node cleanPredictionData.js --all
```

3. 删除指定交易对的数据:
```bash
node cleanPredictionData.js --symbol=BTCUSDT
```

4. 删除指定日期范围的数据:
```bash
node cleanPredictionData.js --after=2024-01-01 --before=2024-04-01
```

5. 只删除非活跃的预测数据:
```bash
node cleanPredictionData.js --inactive
```

6. 组合多个条件:
```bash
node cleanPredictionData.js --symbol=BTCUSDT --before=2024-05-01 --inactive
```

### 注意事项

- 执行删除操作前，脚本会显示将要删除的数据数量并请求确认
- 脚本需要在项目的backend目录下运行
- 需要确保MongoDB连接配置正确（默认从.env文件读取） 