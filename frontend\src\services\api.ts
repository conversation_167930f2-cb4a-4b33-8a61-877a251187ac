import { KLineData } from '../types/chartTypes';
import { Time } from 'lightweight-charts';
import api from '../api/config'; // 使用统一的axios实例

/**
 * 获取30分钟K线数据
 * 从后端API获取比特币的30分钟K线数据
 * @param {number} endTime 可选参数，获取指定时间戳之前的数据
 * @returns {Promise<KLineData[]>} 返回格式化的K线数据数组
 */
export const fetchThirtyMinKlineData = async (endTime?: number): Promise<KLineData[]> => {
  try {
    let url = `/thirty-min-klines`;

    // 如果提供了endTime参数，添加到查询字符串中
    if (endTime) {
      url += `?endTime=${endTime}`;
    }

    const response = await api.get(url);
    
    // 格式化K线数据
    const formattedData = response.data.map((item: any) => ({
      time: item.time,
      open: Number(item.open),
      high: Number(item.high),
      low: Number(item.low),
      close: Number(item.close),
      volume: Number(item.volume),
    }));
    
    // 按时间升序排序
    formattedData.sort((a: any, b: any) => {
      const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
      const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
      return timeA - timeB;
    });
    
    console.log('API获取并排序后的30分钟K线数据:', formattedData.length, '条');
    return formattedData;
  } catch (error) {
    console.error('获取30分钟K线数据时出错:', error);
    throw error;
  }
};
/**
 * 获取预测数据
 * 从后端API获取比特币价格预测数据
 * @param {number} endTime 可选参数，获取指定时间戳之前的预测数据
 * @returns {Promise<any>} 返回格式化的预测数据数组
 */
export const fetchPredictions = async (endTime?: number) => {
  try {
    let url = `/predictions`;

    // 如果提供了endTime参数，添加到查询字符串中
    if (endTime) {
      url += `?endTime=${endTime}`;
    }

    const response = await api.get(url);
    
    // 格式化并确保数据按时间升序排序
    const formattedData = response.data.map((item: any) => ({
      time: item.time,
      open: Number(item.open),
      high: Number(item.high),
      low: Number(item.low),
      close: Number(item.close),
      isActive: item.isActive,
    }));
    
    // 按时间升序排序
    formattedData.sort((a: any, b: any) => {
      const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
      const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
      return timeA - timeB;
    });
    
    console.log('API获取并排序后的预测数据:', formattedData.length, '条');
    return formattedData;
  } catch (error) {
    console.error('获取预测数据时出错:', error);
    throw error;
  }
};

/**
 * 直接从币安API获取最新的两根30分钟K线数据
 * @returns {Promise<KLineData[]>} 最新的两根30分钟K线数据
 */
export const fetchLatestTwoKlinesFromBinance = async (): Promise<KLineData[]> => {
  try {
    const symbol = 'BTCUSDT';
    const interval = '30m'; // 30分钟K线
    const limit = 5; // 获取最新的两条数据
    
    console.log('从币安API直接获取最新5根30分钟K线数据...');
    
    // 发送请求到币安API
    const response = await fetch(
      `https://api.binance.com/api/v3/klines?symbol=${symbol}&interval=${interval}&limit=${limit}`
    );
    
    if (!response.ok) {
      throw new Error(`获取币安实时K线失败: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (!data || data.length === 0) {
      console.warn('币安API未返回K线数据');
      return [];
    }
    
    // 格式化K线数据
    const formattedData = data.map((kline: any) => ({
      time: Math.floor(kline[0] / 1000) as Time, // 转换为秒级时间戳
      open: parseFloat(kline[1]),
      high: parseFloat(kline[2]),
      low: parseFloat(kline[3]),
      close: parseFloat(kline[4]),
      volume: parseFloat(kline[5])
    }));
    
    console.log(`从币安API获取到 ${formattedData.length} 条最新30分钟K线数据`);
    return formattedData;
  } catch (error) {
    console.error('从币安API获取实时K线数据出错:', error);
    return []; // 出错时返回空数组
  }
};

/**
 * 获取蜡烛图数据
 * 此函数获取后端的30分钟K线历史数据，并从币安API获取最新的两根K线，合并后返回
 * @returns {Promise<KLineData[]>} 返回格式化并合并了实时数据的K线数据数组
 */
export const fetchCandleData = async (): Promise<KLineData[]> => {
  try {
    // 并行获取后端历史数据和币安最新数据
    const [historicalData, latestBinanceKlines] = await Promise.all([
      fetchThirtyMinKlineData(), // 获取后端历史数据
      fetchLatestTwoKlinesFromBinance() // 获取币安最新两根K线
    ]);
    
    // 如果历史数据获取失败，且币安数据也获取失败，则抛出错误
    if ((!historicalData || historicalData.length === 0) && 
        (!latestBinanceKlines || latestBinanceKlines.length === 0)) {
      throw new Error('无法获取K线数据，后端和币安API都返回了空数据');
    }
    
    // 合并数据（使用Map确保不重复）
    const dataMap = new Map();
    
    // 先处理历史数据
    if (historicalData && historicalData.length > 0) {
      historicalData.forEach(item => {
        const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
        dataMap.set(timeKey, item);
      });
      console.log(`后端获取到 ${historicalData.length} 条历史30分钟K线数据`);
    }
    
    // 再处理币安最新数据（优先使用币安的最新数据）
    if (latestBinanceKlines && latestBinanceKlines.length > 0) {
      latestBinanceKlines.forEach(item => {
        const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
        // 如果是相同时间点的K线，使用币安的数据覆盖后端数据
        dataMap.set(timeKey, {
          ...item,
          // 添加一个标记，表示这是从币安API直接获取的最新数据
          fromBinance: true
        });
      });
    }
    
    // 转换回数组并排序
    const combinedData = Array.from(dataMap.values()) as KLineData[];
    combinedData.sort((a, b) => {
      const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
      const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
      return timeA - timeB;
    });
    
    console.log(`合并后共有 ${combinedData.length} 条30分钟K线数据`);
    
    // 查看最新几条数据的时间，便于调试
    if (combinedData.length > 0) {
      const lastItems = combinedData.slice(-2);
      lastItems.forEach(item => {
        const timeStr = new Date(Number(item.time) * 1000).toISOString();
        console.log(`K线数据: ${timeStr}, 源: ${(item as any).fromBinance ? '币安API' : '后端'}`);
      });
    }
    
    return combinedData;
  } catch (error) {
    console.error('获取蜡烛图数据时出错:', error);
    throw error;
  }
};

/**
 * 获取预测折线数据
 * 从后端API获取基于节气周期的预测折线数据
 * @param {number} endTime 可选参数，获取指定时间戳之前的预测数据
 * @returns {Promise<any>} 返回预测折线数据数组
 */
export const fetchPredictionLines = async (endTime?: number) => {
  try {
    let url = `/solar-term-predictions`;

    // 如果提供了endTime参数，添加到查询字符串中
    if (endTime) {
      url += `?endTime=${endTime}`;
    }

    const response = await api.get(url);
    
    if (!response.data || !Array.isArray(response.data)) {
      console.warn('预测折线数据格式异常:', response.data);
      return [];
    }
    
    // 确保数据格式正确
    const formattedData = response.data.map((item: any) => ({
      time: typeof item.time === 'string' ? parseInt(item.time) : item.time,
      value: typeof item.value === 'string' ? parseFloat(item.value) : item.value,
      isActive: true
    }));
    
    // 按时间升序排序
    formattedData.sort((a: any, b: any) => a.time - b.time);
    
    console.log('API获取预测折线数据:', formattedData.length, '条');
    return formattedData;
  } catch (error) {
    console.error('获取预测折线数据时出错:', error);
    throw error;
  }
};

// 默认导出统一的axios实例，供其他模块使用
export default api;