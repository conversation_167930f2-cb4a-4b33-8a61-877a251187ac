import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import useAdminStore from '../../store/useAdminStore';
import { BarChart2, Users, Bell, Settings, LogOut, CreditCard, ChevronDown, Globe, UserCog, CreditCard as Payment, Shield, MessageSquare, Menu, X } from 'lucide-react';
import { Toaster } from '../ui/toaster';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [settingsMenuOpen, setSettingsMenuOpen] = useState(false);
  const { logout, user } = useAdminStore();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = () => {
    logout();
    navigate('/admin/login');
  };

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };

  const isSettingsActive = () => {
    return location.pathname.startsWith('/admin/settings');
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* 移动端侧边栏遮罩 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* 侧边栏 */}
      <aside
        className={`${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } fixed inset-y-0 left-0 z-50 w-64 transition-transform duration-300 transform bg-white border-r border-gray-200 lg:translate-x-0 lg:static lg:inset-0`}
      >
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <BarChart2 className="h-6 w-6 text-blue-600" />
            <span className="text-lg font-semibold text-gray-800">管理后台</span>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <nav className="px-4 py-4">
          <Link
            to="/admin/dashboard"
            className={`flex items-center px-4 py-2 mb-1 rounded-md transition-colors ${
              isActive('/admin/dashboard')
                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
            onClick={() => setSidebarOpen(false)}
          >
            <BarChart2 className="h-5 w-5 mr-3" />
            <span>仪表盘</span>
          </Link>

          <Link
            to="/admin/users"
            className={`flex items-center px-4 py-2 mb-1 rounded-md transition-colors ${
              isActive('/admin/users')
                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
            onClick={() => setSidebarOpen(false)}
          >
            <Users className="h-5 w-5 mr-3" />
            <span>用户管理</span>
          </Link>

          <Link
            to="/admin/payments"
            className={`flex items-center px-4 py-2 mb-1 rounded-md transition-colors ${
              isActive('/admin/payments')
                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
            onClick={() => setSidebarOpen(false)}
          >
            <CreditCard className="h-5 w-5 mr-3" />
            <span>支付管理</span>
          </Link>

          <Link
            to="/admin/announcements"
            className={`flex items-center px-4 py-2 mb-1 rounded-md transition-colors ${
              isActive('/admin/announcements')
                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
            onClick={() => setSidebarOpen(false)}
          >
            <Bell className="h-5 w-5 mr-3" />
            <span>公告管理</span>
          </Link>

          <Link
            to="/admin/feedback"
            className={`flex items-center px-4 py-2 mb-1 rounded-md transition-colors ${
              isActive('/admin/feedback')
                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
            onClick={() => setSidebarOpen(false)}
          >
            <MessageSquare className="h-5 w-5 mr-3" />
            <span>用户反馈</span>
          </Link>

          <div className="mt-4">
            <button
              className={`flex items-center justify-between w-full px-4 py-2 mb-1 rounded-md transition-colors ${
                isSettingsActive()
                  ? 'bg-blue-50 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
              onClick={() => setSettingsMenuOpen(!settingsMenuOpen)}
            >
              <div className="flex items-center">
                <Settings className="h-5 w-5 mr-3" />
                <span>系统设置</span>
              </div>
              <ChevronDown className={`h-4 w-4 transition-transform ${settingsMenuOpen ? 'rotate-180' : ''}`} />
            </button>

            {/* 系统设置子菜单 */}
            <div className={`ml-4 space-y-1 overflow-hidden transition-all ${settingsMenuOpen ? 'max-h-80' : 'max-h-0'}`}>
              <Link
                to="/admin/settings/basic"
                className={`flex items-center px-4 py-2 rounded-md transition-colors ${
                  isActive('/admin/settings/basic')
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => setSidebarOpen(false)}
              >
                <Globe className="h-4 w-4 mr-3" />
                <span className="text-sm">基础设置</span>
              </Link>

              <Link
                to="/admin/settings/user"
                className={`flex items-center px-4 py-2 rounded-md transition-colors ${
                  isActive('/admin/settings/user')
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => setSidebarOpen(false)}
              >
                <UserCog className="h-4 w-4 mr-3" />
                <span className="text-sm">用户设置</span>
              </Link>

              <Link
                to="/admin/settings/payment"
                className={`flex items-center px-4 py-2 rounded-md transition-colors ${
                  isActive('/admin/settings/payment')
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => setSidebarOpen(false)}
              >
                <Payment className="h-4 w-4 mr-3" />
                <span className="text-sm">支付设置</span>
              </Link>

              <Link
                to="/admin/settings/security"
                className={`flex items-center px-4 py-2 rounded-md transition-colors ${
                  isActive('/admin/settings/security')
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => setSidebarOpen(false)}
              >
                <Shield className="h-4 w-4 mr-3" />
                <span className="text-sm">安全设置</span>
              </Link>
            </div>
          </div>
        </nav>

        <div className="absolute bottom-0 w-full p-4 border-t border-gray-200">
          <button
            onClick={handleLogout}
            className="flex items-center w-full px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors"
          >
            <LogOut className="h-5 w-5 mr-3" />
            <span>退出登录</span>
          </button>
        </div>
      </aside>

      <div className="flex flex-col flex-1 overflow-hidden">
        {/* 顶部导航栏 */}
        <header className="h-16 bg-white border-b border-gray-200">
          <div className="flex items-center justify-between h-full px-6">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(true)}
                className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-2 rounded-md lg:hidden"
              >
                <Menu className="w-5 h-5" />
              </button>
            </div>

            <div className="flex items-center">
              <div className="relative">
                <span className="text-sm text-gray-600">{user?.email}</span>
              </div>
            </div>
          </div>
        </header>

        {/* 主内容区域 */}
        <main className="flex-1 overflow-auto bg-gray-50">
          {children}
        </main>
      </div>
      <Toaster />
    </div>
  );
};

export default AdminLayout;