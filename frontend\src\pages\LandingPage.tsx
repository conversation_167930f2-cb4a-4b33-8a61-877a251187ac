import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { BarChart2, TrendingUp, Clock, Activity } from 'lucide-react';

const LandingPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-background to-background-light">
      {/* 背景网格效果 */}
      <div className="absolute inset-0 z-0 opacity-10" 
        style={{
          backgroundImage: `radial-gradient(rgba(59, 130, 246, 0.2) 1px, transparent 1px)`,
          backgroundSize: '30px 30px'
        }}>
      </div>

      {/* 主内容区 */}
      <div className="relative z-10 flex flex-col min-h-screen">
        {/* 顶部内容 - 占满屏幕高度 */}
        <div className="flex-1 flex flex-col lg:flex-row items-center px-6 lg:px-20 py-12 lg:py-0">
          {/* 左侧文字 */}
          <div className="lg:w-1/2 text-center lg:text-left lg:pr-10">
            <div className="inline-block px-3 py-1 mb-6 rounded-full bg-primary/10 text-primary text-sm font-medium">
              {t('landing.tag', '基于AI的高级分析工具')}
            </div>
            
            <h1 className="text-4xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              {t('landing.title', 'BTC 价格预测系统')}
            </h1>
            
            <p className="text-xl text-content-secondary mb-8 max-w-2xl">
              {t('landing.description', '基于先进的机器学习算法，为您提供精准的 BTC 价格预测服务，帮助您做出更明智的投资决策')}
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4 mb-12">
              <Link
                to="/register"
                className="w-full sm:w-auto px-8 py-3 rounded-lg bg-gradient-to-r from-primary to-accent hover:shadow-glow text-white font-medium transition-all"
              >
                {t('landing.startNow', '立即开始')}
              </Link>
              
              <Link
                to="/features"
                className="w-full sm:w-auto px-8 py-3 rounded-lg border border-border bg-white/5 hover:bg-white/10 text-content-primary font-medium transition-colors"
              >
                {t('landing.learnMore', '了解更多')}
              </Link>
            </div>
          </div>
          
          {/* 右侧图表/手机模拟 */}
          <div className="lg:w-1/2 relative mt-8 lg:mt-0">
            <div className="relative bg-background-card/80 backdrop-blur-md border border-border rounded-xl p-6 shadow-card">
              <div className="h-[350px] w-full bg-gradient-to-b from-primary/5 to-accent/5 rounded-lg overflow-hidden relative">
                <img src="/images/chart-mockup.svg" alt="BTC价格图表" className="w-full h-full object-cover" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-4/5 h-[200px] bg-primary/10 animate-pulse rounded-lg"></div>
                </div>
              </div>
              
              {/* 特性标记点 */}
              <div className="absolute top-1/4 left-1/4 flex items-center">
                <div className="w-3 h-3 rounded-full bg-primary"></div>
                <div className="w-20 h-px bg-primary ml-1"></div>
                <div className="ml-2 text-sm text-primary bg-background-card px-2 py-1 rounded-md">实时数据</div>
              </div>
              
              <div className="absolute bottom-1/3 right-10 flex items-center">
                <div className="w-3 h-3 rounded-full bg-accent"></div>
                <div className="w-20 h-px bg-accent ml-1"></div>
                <div className="ml-2 text-sm text-accent bg-background-card px-2 py-1 rounded-md">AI预测</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandingPage; 