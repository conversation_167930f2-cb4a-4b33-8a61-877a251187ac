import ThirtyMinKline from '../models/ThirtyMinKline';
import { IThirtyMinKline } from '../models/ThirtyMinKline';
import binanceService from './binanceService';


class ThirtyMinKlineService {
  /**
   * 获取并保存最近5条完整30分钟K线，刷新任务用
   */
  async fetchLatestCompletedKlines(symbol: string = 'BTCUSDT'): Promise<number> {
    try {
      const now = Date.now();
      const startTime = now - 5 * 30 * 60 * 1000; // 5根，150分钟前
      const alignedStart = Math.floor(startTime / 1800000) * 1800000; // 对齐30分钟整数点

      const response = await binanceService.sendRequest('/api/v3/klines', {
        symbol,
        interval: '30m',
        startTime: alignedStart,
        limit: 5
      });

      const binanceKlines = response.data;
      if (!binanceKlines || binanceKlines.length === 0) {
        console.warn(`[WARN] 没拉到币安5根30分钟K线`);
        return 0;
      }

      let savedCount = 0;
      for (const kline of binanceKlines) {
        const openTime = kline[0];
        const local = await ThirtyMinKline.findOne({ symbol, openTime });

        const isDirty = local &&
          (local.open !== kline[1] ||
           local.high !== kline[2] ||
           local.low !== kline[3] ||
           local.close !== kline[4]);

        if (isDirty) {
          await ThirtyMinKline.deleteOne({ symbol, openTime });
          console.log(`[WARN] 删除脏数据 openTime=${new Date(openTime).toISOString()}`);
        }
        if (!local || isDirty) {
          const newKline = new ThirtyMinKline({
            symbol,
            openTime,
            closeTime: kline[6],
            open: kline[1],
            high: kline[2],
            low: kline[3],
            close: kline[4],
            volume: kline[5],
            quoteVolume: kline[7],
            trades: kline[8]
          });
          await newKline.save();
          console.log(`[SUCCESS] 保存K线 openTime=${new Date(openTime).toISOString()}`);
          savedCount++;
        }
      }
      return savedCount;
    } catch (error) {
      console.error(`[ERROR] fetchLatestCompletedKlines 失败:`, error);
      return 0;
    }
  }

  /**
   * 检查并修复最近3天的30分钟K线数据
   * 补齐缺失，修复脏数据
   */
  async checkAndFillHistoricalData(symbol: string = 'BTCUSDT'): Promise<number> {
    try {
      console.log(`[INFO] 开始检查最近3天30分钟K线完整性...`);

      const now = Date.now();
      const threeDaysAgo = now - 3 * 24 * 60 * 60 * 1000;
      const alignedStart = Math.floor(threeDaysAgo / 1800000) * 1800000;

      const response = await binanceService.sendRequest('/api/v3/klines', {
        symbol,
        interval: '30m',
        startTime: alignedStart,
        limit: 1000 // 足够覆盖3天
      });

      const binanceKlines = response.data;
      if (!binanceKlines || binanceKlines.length === 0) {
        console.warn(`[WARN] 没拉到币安30分钟历史K线`);
        return 0;
      }

      let fixedCount = 0;
      for (const kline of binanceKlines) {
        const openTime = kline[0];
        const local = await ThirtyMinKline.findOne({ symbol, openTime });

        const isDirty = local &&
          (local.open !== kline[1] ||
           local.high !== kline[2] ||
           local.low !== kline[3] ||
           local.close !== kline[4]);

        if (isDirty) {
          await ThirtyMinKline.deleteOne({ symbol, openTime });
          console.log(`[WARN] 删除脏数据 openTime=${new Date(openTime).toISOString()}`);
        }
        if (!local || isDirty) {
          const newKline = new ThirtyMinKline({
            symbol,
            openTime,
            closeTime: kline[6],
            open: kline[1],
            high: kline[2],
            low: kline[3],
            close: kline[4],
            volume: kline[5],
            quoteVolume: kline[7],
            trades: kline[8]
          });
          await newKline.save();
          console.log(`[SUCCESS] 补齐保存K线 openTime=${new Date(openTime).toISOString()}`);
          fixedCount++;
        }
      }

      console.log(`[INFO] 最近3天30分钟K线检查完成，共修复${fixedCount}条`);
      return fixedCount;
    } catch (error) {
      console.error(`[ERROR] checkAndFillHistoricalData 失败:`, error);
      return 0;
    }
  }



  /**
   * 获取最近的30分钟K线数据
   * 从数据库获取最近的30分钟K线数据
   * 
   * @param symbol 交易对符号，默认为BTCUSDT
   * @param limit 返回K线数量，默认为300
   * @param endTime 结束时间戳，如提供则获取该时间之前的数据
   * @returns 返回按时间升序排序的K线数据数组
   */
  async getRecentKlines(symbol: string = 'BTCUSDT', limit: number = 300, endTime?: number): Promise<IThirtyMinKline[]> {
    try {
      // 构建查询条件
      const query: any = { symbol };
      
      // 如果提供了endTime参数，添加时间条件
      if (endTime) {
        query.openTime = { $lt: endTime };
      }
      
      // 查询数据库 - 确保严格按照limit限制返回数量
      const klines = await ThirtyMinKline.find(query)
        .sort({ openTime: -1 })  // 按开盘时间降序排序
        .limit(limit)
        .lean();  // 返回纯JavaScript对象
      
      // 按时间升序排序后返回
      return klines.sort((a, b) => a.openTime - b.openTime);
    } catch (error) {
      console.error('获取最近30分钟K线数据时出错:', error);
      throw error;
    }
  }
}

// 导出服务实例
export default new ThirtyMinKlineService(); 