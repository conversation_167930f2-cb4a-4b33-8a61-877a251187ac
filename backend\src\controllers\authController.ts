import { Request, Response } from 'express';
import User from '../models/User';
import InviteCode from '../models/InviteCode';
import { generateToken, generateAccessToken, generateRefreshToken, verifyRefreshToken } from '../middlewares/authMiddleware';
import { sendVerificationEmail, verifyEmailToken, sendPasswordResetEmail, verifyPasswordResetToken } from '../utils/sendVerificationEmail';
import mongoose from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { AuthErrorCode } from '../types/errors';
import systemSettingsService from '../services/systemSettingsService';
import inviteRewardService from '../services/inviteRewardService';
import configService from '../services/configService';
import notificationService from '../services/notificationService';

/**
 * 注册新用户
 */
export const register = async (req: Request, res: Response) => {
  try {
    const { email, password, inviteCode } = req.body;

    // 验证必要字段
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: '请提供邮箱和密码'
      });
    }

    // 检查是否允许注册
    const isRegistrationEnabled = await systemSettingsService.isRegistrationEnabled();
    if (!isRegistrationEnabled) {
      return res.status(403).json({
        success: false,
        message: '系统当前不接受新用户注册'
      });
    }

    // 检查邮箱是否已注册
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '该邮箱已被注册'
      });
    }

    // 检查密码复杂度
    const passwordMinLength = await systemSettingsService.getPasswordMinLength();
    if (password.length < passwordMinLength) {
      return res.status(400).json({
        success: false,
        message: `密码长度不能少于${passwordMinLength}位`
      });
    }

    // 检查是否需要特殊字符
    const passwordRequireSpecialChar = await systemSettingsService.isPasswordRequireSpecialChar();
    if (passwordRequireSpecialChar && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return res.status(400).json({
        success: false,
        message: '密码必须包含至少一个特殊字符'
      });
    }

    // 检查邮箱域名限制
    const allowedEmailDomains = await systemSettingsService.getAllowedEmailDomains();
    if (allowedEmailDomains.length > 0) {
      const emailDomain = email.split('@')[1];
      if (!allowedEmailDomains.includes(emailDomain)) {
        return res.status(400).json({
          success: false,
          message: '不支持的邮箱域名'
        });
      }
    }

   /*   // 获取注册 IP
    const registeredIp = req.ip;

  // 检查同一 IP 的注册数量限制
    const ipRegistrationsCount = await User.countDocuments({ registeredIp });
    if (ipRegistrationsCount >= 2) {
      return res.status(403).json({
        success: false,
        message: '同一 IP 最多注册 2 个账号'
      });
    }
    */
    // 验证邀请码
    let invitedBy: mongoose.Types.ObjectId | undefined;

    // 从系统设置中获取是否需要邀请码
    const inviteCodeRequired = await systemSettingsService.isInviteCodeRequired();

    if (inviteCode) {
      const inviteCodeDoc = await InviteCode.findOne({ code: inviteCode, usedBy: null });

      if (!inviteCodeDoc) {
        return res.status(400).json({
          success: false,
          message: '无效的邀请码'
        });
      }

      invitedBy = inviteCodeDoc.createdBy;
    } else if (inviteCodeRequired) {
      return res.status(400).json({
        success: false,
        message: '注册需要邀请码'
      });
    }

    // 从系统设置中获取默认试用天数
    const trialDays = await systemSettingsService.getDefaultTrialDays();

    // 计算试用期结束时间
    const trialEndsAt = new Date();
    trialEndsAt.setDate(trialEndsAt.getDate() + trialDays);

    // 从邮箱生成用户名，确保唯一性
    // 提取邮箱前缀作为用户名基础
    const emailPrefix = email.split('@')[0];
    // 添加随机字符串确保唯一性
    const uniqueUsername = `${emailPrefix}_${uuidv4().substring(0, 8)}`;

    // 从系统设置中获取默认注册角色
    const defaultRole = await systemSettingsService.getDefaultRole();

    // 创建用户
    const newUser = new User({
      email,
      username: uniqueUsername, // 设置唯一用户名
      password,
      role: defaultRole,
      trialEndsAt,
      isVerified: false,
      registeredIp: false,
      invitedBy,
      createdAt: new Date()
    });

    await newUser.save();

    // 如果有使用邀请码，更新邀请码状态
    if (inviteCode) {
      await InviteCode.findOneAndUpdate(
        { code: inviteCode },
        {
          usedBy: newUser._id,
          usedAt: new Date()
        }
      );
    }

    // 为用户生成5个邀请码
    await InviteCode.generateCodesForUser(newUser._id, 5);

    // 发送验证邮件
    await sendVerificationEmail(email, (newUser._id as mongoose.Types.ObjectId).toString());

    // 创建注册成功获得试用通知
    try {
      await notificationService.createTrialGrantedNotification(newUser._id, trialDays);
    } catch (error) {
      console.error('创建试用通知失败:', error);
      // 不影响注册流程，仅记录错误
    }

    return res.status(201).json({
      success: true,
      message: '注册成功，请查收验证邮件'
    });
  } catch (error) {
    console.error('注册失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 用户登录
 */
export const login = async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    // 验证必要字段
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: '请提供邮箱和密码'
      });
    }

    // 查找用户
    const user = await User.findOne({ email });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '邮箱或密码错误'
      });
    }

    // 检查账号是否被锁定
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      // 计算剩余锁定时间（分钟）
      const remainingMinutes = Math.ceil((user.lockedUntil.getTime() - Date.now()) / (60 * 1000));

      return res.status(403).json({
        success: false,
        message: `账号已被锁定，请在 ${remainingMinutes} 分钟后重试`
      });
    }

    // 验证密码
    const isPasswordValid = await user.comparePassword(password);

    // 从配置服务获取登录失败限制次数和锁定时间
    const loginFailLimit = await configService.get('securitySettings.loginFailLimit', 5);
    const loginLockTime = await configService.get('securitySettings.loginLockTime', 2);

    if (!isPasswordValid) {
      // 增加登录失败计数
      user.loginFailCount = (user.loginFailCount || 0) + 1;

      // 如果达到失败限制次数，锁定账号
      if (user.loginFailCount >= loginFailLimit) {
        const lockUntil = new Date();
        lockUntil.setHours(lockUntil.getHours() + loginLockTime); // 锁定指定小时
        user.lockedUntil = lockUntil;

        await user.save();

        return res.status(403).json({
          success: false,
          message: `由于多次登录失败，账号已被锁定 ${loginLockTime} 小时`
        });
      }

      await user.save();

      return res.status(401).json({
        success: false,
        message: `邮箱或密码错误，还有 ${loginFailLimit - user.loginFailCount} 次尝试机会`
      });
    }

    // 检查用户是否被封禁
    if (user.role === 'banned') {
      return res.status(403).json({
        success: false,
        message: '您的账号已被封禁，请联系管理员'
      });
    }

    // 检查邮箱是否已验证
    if (!user.isVerified) {
      return res.status(403).json({
        success: false,
        message: '请先验证您的邮箱'
      });
    }

    // 登录成功，重置登录失败计数和锁定时间
    user.loginFailCount = 0;
    user.lockedUntil = undefined;

    // 更新最后登录时间、最后活跃时间和在线状态
    user.lastLoginAt = new Date();
    user.lastActiveAt = new Date(); // 设置最后活跃时间
    user.isOnline = true;
    await user.save();

    const userId = (user._id as mongoose.Types.ObjectId).toString();

    // 生成访问令牌和刷新令牌
    const accessToken = generateAccessToken(userId);
    const refreshToken = generateRefreshToken(userId);

    // 设置刷新令牌到 HttpOnly Cookie，有效期为2小时
    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production', // 在生产环境中使用 HTTPS
      sameSite: 'lax',  // 改为lax模式以允许跨站请求带上Cookie
      path: '/',        // 确保路径设置正确
      maxAge: 2 * 60 * 60 * 1000 // 2小时有效期
    });

    // 去除密码字段再返回用户信息
    const userResponse = {
      _id: user._id,
      email: user.email,
      role: user.role,
      trialEndsAt: user.trialEndsAt,
      isVerified: user.isVerified,
      createdAt: user.createdAt,
      lastLoginAt: user.lastLoginAt,
      lastActiveAt: user.lastActiveAt,
      isOnline: user.isOnline,
      invitedBy: user.invitedBy,
      subscription: user.subscription,
      rewardHistory: user.rewardHistory
    };

    return res.status(200).json({
      success: true,
      message: '登录成功',
      token: accessToken, // 为了向后兼容，保留 token 字段名
      accessToken, // 同时提供新字段名
      user: userResponse
    });
  } catch (error) {
    console.error('登录失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 验证邮箱
 */
export const verifyEmail = async (req: Request, res: Response) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: '无效的验证链接'
      });
    }

    // 验证 token
    const decoded = verifyEmailToken(token);

    if (!decoded) {
      return res.status(400).json({
        success: false,
        message: '验证链接已失效或不正确'
      });
    }

    // 更新用户验证状态
    const user = await User.findByIdAndUpdate(
      decoded.userId,
      { isVerified: true },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 如果用户是通过邀请注册的，检查邀请人是否达到奖励条件
    if (user.invitedBy) {
      // 异步处理邀请奖励，不阻塞当前请求
      inviteRewardService.processInviteReward(user.invitedBy)
        .catch(err => console.error('处理邀请奖励失败:', err));
    }

    return res.status(200).json({
      success: true,
      message: '邮箱验证成功'
    });
  } catch (error) {
    console.error('邮箱验证失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 忘记密码 - 发送重置密码邮件
 */
export const forgotPassword = async (req: Request, res: Response) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: '请提供邮箱地址'
      });
    }

    // 查找用户
    const user = await User.findOne({ email });

    // 即使用户不存在，也返回成功消息，避免泄露用户信息
    if (!user) {
      return res.status(200).json({
        success: true,
        message: '如果邮箱已注册，重置密码邮件将发送到该邮箱'
      });
    }

    // 发送重置密码邮件
    await sendPasswordResetEmail(email, (user._id as mongoose.Types.ObjectId).toString());

    return res.status(200).json({
      success: true,
      message: '如果邮箱已注册，重置密码邮件将发送到该邮箱'
    });
  } catch (error) {
    console.error('忘记密码处理失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 重置密码
 */
export const resetPassword = async (req: Request, res: Response) => {
  try {
    const { token, newPassword } = req.body;

    if (!token || !newPassword) {
      return res.status(400).json({
        success: false,
        message: '请提供重置令牌和新密码'
      });
    }

    // 密码长度验证 - 使用系统设置中的密码最小长度
    const passwordMinLength = await systemSettingsService.getPasswordMinLength();
    if (newPassword.length < passwordMinLength) {
      return res.status(400).json({
        success: false,
        message: `密码长度不能少于${passwordMinLength}位`
      });
    }

    // 检查是否需要特殊字符
    const passwordRequireSpecialChar = await systemSettingsService.isPasswordRequireSpecialChar();
    if (passwordRequireSpecialChar && !/[!@#$%^&*(),.?":{}|<>]/.test(newPassword)) {
      return res.status(400).json({
        success: false,
        message: '密码必须包含至少一个特殊字符'
      });
    }

    // 验证令牌
    const decoded = verifyPasswordResetToken(token);

    if (!decoded) {
      return res.status(400).json({
        success: false,
        message: '重置链接已失效或不正确'
      });
    }

    // 查找用户并更新密码
    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 更新密码
    user.password = newPassword;
    await user.save(); // 密码会在save前自动哈希

    return res.status(200).json({
      success: true,
      message: '密码重置成功，请使用新密码登录'
    });
  } catch (error) {
    console.error('重置密码失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 获取当前用户信息
 */
export const getMe = async (req: Request, res: Response) => {
  try {
    // req.user 由认证中间件设置
    const user = req.user;

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '未认证'
      });
    }

    // 排除密码等敏感字段
    const userResponse = {
      _id: user._id,
      email: user.email,
      role: user.role,
      trialEndsAt: user.trialEndsAt,
      isVerified: user.isVerified,
      createdAt: user.createdAt,
      lastLoginAt: user.lastLoginAt,
      lastActiveAt: user.lastActiveAt,
      isOnline: user.isOnline,
      invitedBy: user.invitedBy,
      subscription: user.subscription,
      rewardHistory: user.rewardHistory
    };

    return res.status(200).json({
      success: true,
      user: userResponse
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 刷新访问令牌
 */
export const refresh = async (req: Request, res: Response) => {
  try {
    // 从 Cookie 中获取刷新令牌
    const refreshToken = req.cookies.refreshToken;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        message: '未提供刷新令牌',
        code: AuthErrorCode.REFRESH_TOKEN_MISSING
      });
    }

    // 验证刷新令牌
    const decoded = verifyRefreshToken(refreshToken);

    if (!decoded) {
      // 清除无效的刷新令牌 Cookie
      res.clearCookie('refreshToken');

      return res.status(401).json({
        success: false,
        message: '刷新令牌无效或已过期',
        code: AuthErrorCode.REFRESH_TOKEN_INVALID
      });
    }

    // 查找用户
    const user = await User.findById(decoded.userId);

    if (!user) {
      res.clearCookie('refreshToken');

      return res.status(401).json({
        success: false,
        message: '用户不存在',
        code: AuthErrorCode.USER_NOT_FOUND
      });
    }

    // 检查用户是否被封禁
    if (user.role === 'banned') {
      res.clearCookie('refreshToken');

      return res.status(403).json({
        success: false,
        message: '您的账号已被封禁，请联系管理员',
        code: AuthErrorCode.USER_BANNED
      });
    }

    // 更新用户最后活跃时间
    user.lastActiveAt = new Date();
    await user.save();

    // 生成新的访问令牌
    const accessToken = generateAccessToken(decoded.userId);

    // 生成新的刷新令牌（令牌轮换）
    const newRefreshToken = generateRefreshToken(decoded.userId);

    // 设置 Cookie，每次刷新都重置为2小时过期时间
    res.cookie('refreshToken', newRefreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production', // 在生产环境中使用 HTTPS
      sameSite: 'lax',  // 改为lax模式以允许跨站请求带上Cookie
      path: '/',        // 确保路径设置正确
      maxAge: 2 * 60 * 60 * 1000 // 2小时有效期
    });

    return res.status(200).json({
      success: true,
      accessToken
    });
  } catch (error) {
    console.error('刷新令牌失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试',
      code: 'common/server-error'
    });
  }
};

/**
 * 修改密码（需要认证）
 */
export const changePassword = async (req: Request, res: Response) => {
  try {
    const { oldPassword, newPassword } = req.body;
    const userId = (req as any).user.id; // 从认证中间件获取用户ID

    if (!oldPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: '请提供当前密码和新密码'
      });
    }

    // 密码长度验证 - 使用系统设置中的密码最小长度
    const passwordMinLength = await systemSettingsService.getPasswordMinLength();
    if (newPassword.length < passwordMinLength) {
      return res.status(400).json({
        success: false,
        message: `新密码长度不能少于${passwordMinLength}位`
      });
    }

    // 检查是否需要特殊字符
    const passwordRequireSpecialChar = await systemSettingsService.isPasswordRequireSpecialChar();
    if (passwordRequireSpecialChar && !/[!@#$%^&*(),.?":{}|<>]/.test(newPassword)) {
      return res.status(400).json({
        success: false,
        message: '新密码必须包含至少一个特殊字符'
      });
    }

    // 查找用户
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 验证当前密码
    const isCurrentPasswordValid = await user.comparePassword(oldPassword);

    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: '当前密码不正确'
      });
    }

    // 更新密码
    user.password = newPassword;
    await user.save(); // 密码会在save前自动哈希

    // 清除刷新令牌Cookie，强制用户重新登录
    res.clearCookie('refreshToken', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/'
    });

    return res.status(200).json({
      success: true,
      message: '密码修改成功，请重新登录',
      requireReauth: true // 标识需要重新认证
    });
  } catch (error) {
    console.error('修改密码失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

export default {
  register,
  login,
  verifyEmail,
  forgotPassword,
  resetPassword,
  getMe,
  refresh,
  changePassword
};