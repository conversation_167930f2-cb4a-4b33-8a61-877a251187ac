/**
 * 预测折线数据清理脚本
 * 
 * 使用方法:
 * node cleanPredictionData.js [选项]
 * 
 * 选项:
 *   --all           删除所有预测折线数据
 *   --symbol=XXX    指定要删除的交易对数据，例如: --symbol=BTCUSDT
 *   --before=XXX    删除指定日期之前的数据，格式: YYYY-MM-DD，例如: --before=2024-05-01
 *   --after=XXX     删除指定日期之后的数据，格式: YYYY-MM-DD，例如: --after=2024-01-01
 *   --inactive      只删除非活跃预测数据
 *   --help          显示帮助信息
 */

require('dotenv').config({ path: '../.env' });
const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');

// 解析命令行参数
const args = process.argv.slice(2);
const options = parseArguments(args);

// 显示帮助信息
if (options.help) {
  console.log(`
预测折线数据清理脚本

使用方法:
node cleanPredictionData.js [选项]

选项:
  --all           删除所有预测折线数据
  --symbol=XXX    指定要删除的交易对数据，例如: --symbol=BTCUSDT
  --before=XXX    删除指定日期之前的数据，格式: YYYY-MM-DD，例如: --before=2024-05-01
  --after=XXX     删除指定日期之后的数据，格式: YYYY-MM-DD，例如: --after=2024-01-01
  --inactive      只删除非活跃预测数据
  --help          显示帮助信息
  `);
  process.exit(0);
}

// 检查是否有删除条件
if (!options.all && !options.symbol && !options.before && !options.after && !options.inactive) {
  console.error('错误: 必须指定至少一个删除条件或使用 --all 删除所有数据');
  console.log('使用 --help 查看帮助信息');
  process.exit(1);
}

// 数据库连接配置
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/btc-prediction';

// 动态导入SolarTermPredictionLine模型
let SolarTermPredictionLine;
try {
  // 检查模型文件是否存在
  const modelPath = path.resolve(__dirname, '../dist/models/SolarTermPredictionLine.js');
  if (fs.existsSync(modelPath)) {
    const modelModule = require(modelPath);
    SolarTermPredictionLine = modelModule.default || modelModule;
  } else {
    console.error('错误: 无法找到SolarTermPredictionLine模型文件');
    process.exit(1);
  }
} catch (error) {
  console.error('导入模型时出错:', error);
  process.exit(1);
}

// 连接到MongoDB
mongoose.connect(MONGODB_URI)
  .then(() => {
    console.log('已连接到MongoDB数据库');
    return cleanPredictionData(options);
  })
  .then(result => {
    console.log(`成功删除 ${result.deletedCount} 条预测折线数据`);
    mongoose.disconnect();
  })
  .catch(error => {
    console.error('删除数据时出错:', error);
    mongoose.disconnect();
    process.exit(1);
  });

/**
 * 清理预测折线数据
 * @param {Object} options 删除选项
 * @returns {Promise<Object>} 删除结果
 */
async function cleanPredictionData(options) {
  // 构建查询条件
  const query = {};
  
  // 按交易对过滤
  if (options.symbol) {
    query.symbol = options.symbol;
  }
  
  // 按日期过滤
  if (options.before || options.after) {
    query.predictionTime = {};
    
    if (options.before) {
      const beforeDate = new Date(options.before);
      beforeDate.setHours(23, 59, 59, 999);
      query.predictionTime.$lt = beforeDate.getTime();
    }
    
    if (options.after) {
      const afterDate = new Date(options.after);
      afterDate.setHours(0, 0, 0, 0);
      query.predictionTime.$gt = afterDate.getTime();
    }
  }
  
  // 只删除非活跃预测
  if (options.inactive) {
    query.isActive = false;
  }
  
  // 删除前显示将要删除的数据
  const count = await SolarTermPredictionLine.countDocuments(query);
  console.log(`将要删除 ${count} 条预测折线数据`);
  
  // 确认删除
  if (count > 0) {
    const confirm = await promptConfirmation(`确定要删除这 ${count} 条数据吗? (y/n) `);
    if (!confirm) {
      console.log('操作已取消');
      return { deletedCount: 0 };
    }
  } else {
    console.log('没有符合条件的数据需要删除');
    return { deletedCount: 0 };
  }
  
  // 执行删除操作
  const result = await SolarTermPredictionLine.deleteMany(query);
  return result;
}

/**
 * 解析命令行参数
 * @param {string[]} args 命令行参数数组
 * @returns {Object} 解析后的选项对象
 */
function parseArguments(args) {
  const options = {
    all: false,
    symbol: null,
    before: null,
    after: null,
    inactive: false,
    help: false
  };
  
  for (const arg of args) {
    if (arg === '--all') {
      options.all = true;
    } else if (arg === '--inactive') {
      options.inactive = true;
    } else if (arg === '--help') {
      options.help = true;
    } else if (arg.startsWith('--symbol=')) {
      options.symbol = arg.split('=')[1];
    } else if (arg.startsWith('--before=')) {
      options.before = arg.split('=')[1];
    } else if (arg.startsWith('--after=')) {
      options.after = arg.split('=')[1];
    }
  }
  
  return options;
}

/**
 * 控制台提示确认
 * @param {string} question 提示问题
 * @returns {Promise<boolean>} 是否确认
 */
function promptConfirmation(question) {
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  return new Promise(resolve => {
    readline.question(question, answer => {
      readline.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
} 