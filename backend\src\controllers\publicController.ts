import { Request, Response } from 'express';
import SystemSettings from '../models/SystemSettings';
import configService from '../services/configService';

/**
 * 获取公共系统设置
 * GET /api/public/settings
 */
export const getPublicSettings = async (req: Request, res: Response) => {
  try {
    // 获取系统设置
    const settings = await SystemSettings.getSettings();

    // 只返回公开的设置信息
    res.json({
      success: true,
      settings: {
        siteInfo: {
          siteName: settings.siteInfo.siteName,
          siteDescription: settings.siteInfo.siteDescription,
          siteKeywords: settings.siteInfo.siteKeywords,
          logoUrl: settings.siteInfo.logoUrl,
          faviconUrl: settings.siteInfo.faviconUrl,
          copyright: settings.siteInfo.copyright,
          socialLinks: settings.siteInfo.socialLinks,
          defaultLanguage: settings.siteInfo.defaultLanguage
        },
        subscriptionPrices: settings.subscriptionPrices,
        updatedAt: settings.updatedAt
      }
    });
  } catch (error) {
    console.error('获取公共系统设置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

export default {
  getPublicSettings
};
