import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import * as adminApi from '../api/admin';

export interface AdminUser {
  _id: string;
  email: string;
  role: string;
  isVerified: boolean;
  createdAt: string;
}

interface AdminState {
  user: AdminUser | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  isInitializing: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  
  // 用户管理
  fetchUsers: (page: number, limit: number, search: string, filters: any) => Promise<any>;
  updateUserRole: (userId: string, role: string) => Promise<any>;
  createUser: (userData: any) => Promise<any>;
  deleteUser: (userId: string) => Promise<any>;
  banUser: (userId: string) => Promise<any>;
  unbanUser: (userId: string) => Promise<any>;
  
  // 系统设置管理
  getSystemSettings: () => Promise<any>;
  updateSystemSettings: (settings: any) => Promise<any>;
  
  // 支付管理
  getPayments: (page: number, limit: number, search: string, filters: any) => Promise<any>;
  getPaymentDetails: (paymentId: string) => Promise<any>;
  updatePaymentStatus: (paymentId: string, status: string) => Promise<any>;
  getPaymentStats: () => Promise<any>;
  
  // 公告管理
  getAnnouncements: () => Promise<any>;
  createAnnouncement: (data: any) => Promise<any>;
  updateAnnouncement: (id: string, data: any) => Promise<any>;
  deleteAnnouncement: (id: string) => Promise<any>;
  
  updateUserSubscription: (userId: string, subscriptionData: any) => Promise<any>;
  
  clearError: () => void;
  initialize: () => void;
}

type AdminPersist = {
  token: string | null;
  user: AdminUser | null;
};

const useAdminStore = create(
  persist<AdminState, AdminPersist>(
    (set, get) => ({
      user: null,
      token: null,
      isLoading: false,
      error: null,
      isAuthenticated: false,
      isInitializing: true,

      // 初始化检查
      initialize: () => {
        const state = get();
        
        // 如果正在重定向，跳过初始化
        if (sessionStorage.getItem('admin_redirecting')) {
          console.log('检测到管理员重定向标志，跳过初始化');
          sessionStorage.removeItem('admin_redirecting');
          set({ 
            isAuthenticated: false, 
            isInitializing: false,
            isLoading: false,
            user: null,
            token: null
          });
          return;
        }
        
        // 检查是否有持久化的 token 和用户信息
        if (state.token && state.user) {
          // 验证用户角色
          if (state.user.role === 'admin') {
            // 设置初始状态，但不进行API请求
            set({ 
              isAuthenticated: true, 
              isInitializing: false,
              user: state.user,
              token: state.token
            });
            
            // 在这里可以添加一个轻量级的验证请求
            // 但避免在初始化中进行数据加载，将数据加载移到具体页面
          } else {
            // 如果不是管理员，清除状态
            console.warn('存储的用户不是管理员，清除认证状态');
            set({ 
              isAuthenticated: false, 
              isInitializing: false,
              user: null,
              token: null
            });
            // 清除存储
            localStorage.removeItem('admin-storage');
          }
        } else {
          // 如果没有持久化的信息，清除所有状态
          console.log('没有找到管理员认证信息');
          set({ 
            isAuthenticated: false, 
            isInitializing: false,
            user: null,
            token: null
          });
        }
      },

      // 管理员登录
      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.adminLogin(email, password);
          
          // 验证用户角色
          if (response.user.role !== 'admin') {
            throw new Error('非管理员账号无法登录');
          }
          
          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            isLoading: false,
            isInitializing: false
          });
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '登录失败，请稍后重试';
          set({ 
            isLoading: false, 
            error: errorMessage, 
            isInitializing: false,
            isAuthenticated: false,
            user: null,
            token: null
          });
          throw error;
        }
      },

      // 登出
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isInitializing: false
        });
      },

      // 获取用户列表
      fetchUsers: async (page = 1, limit = 10, search = '', filters = {}) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.getUsers(page, limit, search, filters);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '获取用户列表失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 更新用户角色
      updateUserRole: async (userId: string, role: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.updateUserRole(userId, role);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '更新用户角色失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 创建用户
      createUser: async (userData: any) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.createUser(userData);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '创建用户失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 删除用户
      deleteUser: async (userId: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.deleteUser(userId);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '删除用户失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 封禁用户
      banUser: async (userId: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.banUser(userId);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '封禁用户失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 解封用户
      unbanUser: async (userId: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.unbanUser(userId);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '解封用户失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 获取系统设置
      getSystemSettings: async () => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.getSystemSettings();
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '获取系统设置失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 更新系统设置
      updateSystemSettings: async (settings: any) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.updateSystemSettings(settings);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '更新系统设置失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 获取支付列表
      getPayments: async (page = 1, limit = 10, search = '', filters = {}) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.getPayments(page, limit, search, filters);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '获取支付列表失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 获取支付详情
      getPaymentDetails: async (paymentId: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.getPaymentDetails(paymentId);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '获取支付详情失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 更新支付状态
      updatePaymentStatus: async (paymentId: string, status: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.updatePaymentStatus(paymentId, status);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '更新支付状态失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 获取支付统计数据
      getPaymentStats: async () => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.getPaymentStats();
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '获取支付统计数据失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 获取公告列表
      getAnnouncements: async () => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.getAnnouncements();
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '获取公告列表失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 创建公告
      createAnnouncement: async (data: any) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.createAnnouncement(data);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '创建公告失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 更新公告
      updateAnnouncement: async (id: string, data: any) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.updateAnnouncement(id, data);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '更新公告失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 删除公告
      deleteAnnouncement: async (id: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.deleteAnnouncement(id);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '删除公告失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 更新用户订阅
      updateUserSubscription: async (userId: string, subscriptionData: any) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.updateUserSubscription(userId, subscriptionData);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '更新用户订阅失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      }
    }),
    {
      name: 'admin-storage', // localStorage 中的键名
      partialize: (state) => {
        const s = state as AdminState;
        return {
          token: s.token,
          user: s.user
        };
      }
    }
  )
);

export default useAdminStore; 