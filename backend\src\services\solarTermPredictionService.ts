import SolarTermPredictionLine, { ISolarTermPredictionLine } from '../models/SolarTermPredictionLine';
import binanceService from './binanceService';
import {
  getBeijingDate,
  formatDate,
  isMonthFirstSolarTermDay,
  getMonthFirstSolarTerm,
  getNextMonthFirstSolarTerm
} from '../utils/solarTermUtils';

/**
 * 节气预测折线服务类
 * 负责生成和管理基于节气周期的预测折线
 * 新版逻辑：根据每月第一个节气日期生成预测折线，每天23:00预测未来3天
 */
class SolarTermPredictionService {
  // 记录上一次预测使用的数据末尾时间，用于后续预测的连续性参考
  private lastPredictionDataEndTime: Map<string, number> = new Map();

  /**
   * 将UTC时间转换为北京时间（UTC+8）
   * @param date UTC时间
   * @returns 北京时间
   */
  private toBeijingTime(date: Date): Date {
    return new Date(date.getTime() + 8 * 60 * 60 * 1000);
  }

  /**
   * 将北京时间转换为UTC时间
   * @param beijingDate 北京时间
   * @returns UTC时间
   */
  private toUTCTime(beijingDate: Date): Date {
    return new Date(beijingDate.getTime() - 8 * 60 * 60 * 1000);
  }

  /**
   * 判断给定日期是否是预测时间点
   *
   * @param date 日期对象（默认UTC+8北京时间）
   * @returns 如果是预测时间点则返回预测信息，否则返回null
   */
  public isPredictionTimePoint(date: Date): {
    isSolarTermDay: boolean;
    cycle: string;
    predictionHours: number;
    isSolarTermDayStart?: boolean; // 是否为节气日当天的开始预测(00:00)
  } | null {
    // 直接使用传入的日期作为北京时间
    const beijingDate = date;
    const year = beijingDate.getFullYear();
    const month = beijingDate.getMonth() + 1; // JS月份从0开始
    const day = beijingDate.getDate();
    const hour = beijingDate.getHours();
    const minute = beijingDate.getMinutes();

    // 只在整点时进行预测（容错判断：允许59分、0分、1分）
    if (minute !== 0 && minute !== 59 && minute !== 1) return null;

    // 判断是否是当月第一个节气日
    const isSolarTermDay = isMonthFirstSolarTermDay(year, month, day);

    // 节气日当日的00:00预测
    if (isSolarTermDay && hour === 0) {
      return {
        isSolarTermDay: true,
        cycle: 'solar_term_day_start',
        predictionHours: 24, // 预测未来24小时
        isSolarTermDayStart: true // 标记为节气日开始
      };
    }

    // 获取当前月和前一个月的节气日信息
    const currentSolarTerm = getMonthFirstSolarTerm(year, month);

    let prevMonthDate = new Date(beijingDate);
    prevMonthDate.setMonth(prevMonthDate.getMonth() - 1);
    const prevYear = prevMonthDate.getFullYear();
    const prevMonth = prevMonthDate.getMonth() + 1;
    const prevSolarTerm = getMonthFirstSolarTerm(prevYear, prevMonth);

    // 确定当前时间所属的节气周期
    let activeSolarTermDate: Date;
    let daysSinceSolarTerm: number;
    let nextMonthSolarTerm: Date | null;

    // 创建当月节气日期对象
    const currentSolarTermDate = currentSolarTerm ?
      new Date(year, month - 1, currentSolarTerm.day) : null;
    currentSolarTermDate?.setHours(0, 0, 0, 0);

    // 创建前月节气日期对象
    const prevSolarTermDate = prevSolarTerm ?
      new Date(prevYear, prevMonth - 1, prevSolarTerm.day) : null;
    prevSolarTermDate?.setHours(0, 0, 0, 0);

    const currentTime = beijingDate.getTime();

    // 判断当前时间属于哪个节气周期
    if (currentSolarTermDate && currentTime >= currentSolarTermDate.getTime()) {
      // 当前时间 >= 当月节气日，属于当月节气周期
      activeSolarTermDate = currentSolarTermDate;
      daysSinceSolarTerm = Math.floor((currentTime - currentSolarTermDate.getTime()) / (24 * 60 * 60 * 1000));
      nextMonthSolarTerm = getNextMonthFirstSolarTerm(date);
    } else if (prevSolarTermDate) {
      // 当前时间 < 当月节气日，属于前月节气周期
      activeSolarTermDate = prevSolarTermDate;
      daysSinceSolarTerm = Math.floor((currentTime - prevSolarTermDate.getTime()) / (24 * 60 * 60 * 1000));
      // 下个节气日就是当月节气日
      nextMonthSolarTerm = currentSolarTermDate;
    } else {
      // 无法确定节气周期
      return null;
    }

    if (!nextMonthSolarTerm) return null;

    // 判断是否是节气日当天的23:00
    if (isSolarTermDay && hour === 23) {
      return {
        isSolarTermDay: true,
        cycle: 'day_0', // 标记为节气日第0天
        predictionHours: 72  // 预测未来72小时（3天）
      };
    }

    // 节气日后每天的23:00都触发预测
    if (daysSinceSolarTerm > 0 && hour === 23) {
      // 计算到下个节气日前一天23:00的剩余小时数
      const nextMonthPrevDay = new Date(nextMonthSolarTerm.getTime() - 24 * 60 * 60 * 1000);
      nextMonthPrevDay.setHours(23, 0, 0, 0);

      const remainingHours = Math.max(0, (nextMonthPrevDay.getTime() - currentTime) / (60 * 60 * 1000));

      // 如果剩余时间小于24小时，则不生成预测
      if (remainingHours < 24) {
        return null;
      }

      // 确定预测时间长度
      let predictionHours = 72; // 标准是72小时（3天）

      // 如果剩余时间不足72小时但大于等于48小时，则预测48小时
      if (remainingHours < 72 && remainingHours >= 48) {
        predictionHours = 48;
      }
      // 如果剩余时间不足48小时但大于等于24小时，则预测24小时
      else if (remainingHours < 48 && remainingHours >= 24) {
        predictionHours = 24;
      }

      return {
        isSolarTermDay: false,
        cycle: `day_${daysSinceSolarTerm}`,
        predictionHours
      };
    }

    return null;
  }

  /**
   * 生成预测折线
   *
   * @param date 当前日期时间（UTC+8北京时间）
   * @param symbol 交易对符号
   * @returns 生成的预测数据，如果当前不是预测时间点则返回null
   */
  public async generatePrediction(date: Date = new Date(Date.now() + 8 * 60 * 60 * 1000), symbol: string = 'BTCUSDT'): Promise<ISolarTermPredictionLine | null> {
    // 使用传入的北京时间
    const beijingDate = date;
    const year = beijingDate.getFullYear();
    const month = beijingDate.getMonth() + 1;

    // 1. 确定预测点所属的节气周期
    // 获取当前月第一个节气日
    const currentSolarTerm = getMonthFirstSolarTerm(year, month);

    // 获取前一个月的节气
    let prevMonthDate = new Date(beijingDate);
    prevMonthDate.setMonth(prevMonthDate.getMonth() - 1);
    const prevYear = prevMonthDate.getFullYear();
    const prevMonth = prevMonthDate.getMonth() + 1;
    const prevSolarTerm = getMonthFirstSolarTerm(prevYear, prevMonth);

    if (!currentSolarTerm && !prevSolarTerm) {
      console.error('无法确定当前或前一个节气日');
      return null;
    }

    // 创建节气日日期对象
    let solarTermDayDate: Date;
    let solarTermYear: number;
    let solarTermMonth: number;
    let solarTermDay: number;

    const currentSolarTermDate = currentSolarTerm ?
      new Date(year, month - 1, currentSolarTerm.day) : null;
    currentSolarTermDate?.setHours(0, 0, 0, 0);

    const prevSolarTermDate = prevSolarTerm ?
      new Date(prevYear, prevMonth - 1, prevSolarTerm.day) : null;
    prevSolarTermDate?.setHours(0, 0, 0, 0);

    // 2. 确定预测点类型
    let predictionPoint;
    if (currentSolarTermDate && beijingDate >= currentSolarTermDate && currentSolarTerm) {
      // 当前月节气周期
      predictionPoint = this.isPredictionTimePoint(date);
      solarTermDayDate = currentSolarTermDate;
      solarTermYear = year;
      solarTermMonth = month;
      solarTermDay = currentSolarTerm.day;
    } else if (prevSolarTermDate && prevSolarTerm) {
      // 前一个月节气周期
        predictionPoint = this.isPredictionTimePoint(date);
      solarTermDayDate = prevSolarTermDate;
      solarTermYear = prevYear;
      solarTermMonth = prevMonth;
      solarTermDay = prevSolarTerm.day;
    } else {
      console.error('无法确定预测点所属节气周期');
      return null;
    }

    // 如果不是预测时间点，返回null
    if (!predictionPoint) {
      return null;
    }

    // 构建节气日日期字符串
    const solarTermDate = formatDate(solarTermYear, solarTermMonth, solarTermDay);

    // 3. 计算源数据时间和目标预测时间
    let sourceStartTimes: number[] = [];
    let sourceEndTimes: number[] = [];
    let sourceMinuteCounts: number[] = [];
    let targetStartTime: number;
    let targetEndTime: number;

    if (predictionPoint.isSolarTermDayStart) {
      // 节气日当日预测（00:00触发）
      // 数据区间：前一天23:00开始的48条1分钟K线（23:00~23:47）
      // 预测区间：前一天23:00到节气日23:00（24小时）

      // 设置目标时间范围
      const prevDay = new Date(beijingDate);
      prevDay.setDate(prevDay.getDate() - 1);
      prevDay.setHours(23, 0, 0, 0);
      targetStartTime = prevDay.getTime();

      const targetEnd = new Date(beijingDate);
      targetEnd.setHours(23, 0, 0, 0);
      targetEndTime = targetEnd.getTime();

      // 设置源数据时间范围（前一天23:00开始的48分钟数据）
      sourceStartTimes.push(targetStartTime);
      sourceMinuteCounts.push(48);
      sourceEndTimes.push(new Date(sourceStartTimes[0] + (sourceMinuteCounts[0] - 1) * 60 * 1000).getTime());

      console.log(`节气日预测(00:00): 使用${new Date(sourceStartTimes[0]).toISOString()} - ${new Date(sourceEndTimes[0]).toISOString()}的${sourceMinuteCounts[0]}条1分钟数据`);
    } else if (predictionPoint.isSolarTermDay) {
      // 节气日当天23:00的第一次常规预测（第0天）
      // 设置目标时间范围（预测未来72小时/3天）
      targetStartTime = date.getTime();
      targetEndTime = new Date(targetStartTime + predictionPoint.predictionHours * 60 * 60 * 1000).getTime();

      // 根据预测天数准备多段源数据
      const daysToPredict = predictionPoint.predictionHours / 24;
      const solarTermPrevDay = new Date(solarTermDayDate);
      solarTermPrevDay.setDate(solarTermPrevDay.getDate() - 1);

      for (let day = 0; day < daysToPredict; day++) {
        // 为每一天预测准备一段48分钟数据
        const startTimeBase = new Date(solarTermPrevDay);
        startTimeBase.setHours(23, 48, 0, 0); // 从23:48开始
        const dayOffset = day * 48 * 60 * 1000; // 每天增加48分钟
        const startTime = startTimeBase.getTime() + dayOffset;

        sourceStartTimes.push(startTime);
        sourceMinuteCounts.push(48);
        sourceEndTimes.push(new Date(startTime + (48 - 1) * 60 * 1000).getTime());

        console.log(`节气日常规预测(23:00) 第${day+1}天: 使用${new Date(startTime).toISOString()} - ${new Date(sourceEndTimes[day]).toISOString()}的48条1分钟数据`);
      }
    } else {
      // 节气日之后的每天23:00预测
      // 设置目标时间范围（预测未来72小时/3天）
      targetStartTime = date.getTime();
      targetEndTime = new Date(targetStartTime + predictionPoint.predictionHours * 60 * 60 * 1000).getTime();

      // 从预测点的cycle提取天数
      const daysSinceSolarTerm = parseInt(predictionPoint.cycle.split('_')[1]);

      // 计算源数据基准：从节气日前一天23:48开始
      const solarTermPrevDay = new Date(solarTermDayDate);
      solarTermPrevDay.setDate(solarTermPrevDay.getDate() - 1);
      solarTermPrevDay.setHours(23, 48, 0, 0); // 从23:48开始

      // 根据预测天数准备多段源数据
      const daysToPredict = predictionPoint.predictionHours / 24;

      for (let day = 0; day < daysToPredict; day++) {
        // 为每一天预测准备一段48分钟数据
        // 基准时间 + 天数偏移 * 48分钟 + 当前预测天偏移 * 48分钟
        const startTime = solarTermPrevDay.getTime() +
                           daysSinceSolarTerm * 48 * 60 * 1000 +
                           day * 48 * 60 * 1000;

        sourceStartTimes.push(startTime);
        sourceMinuteCounts.push(48);
        sourceEndTimes.push(new Date(startTime + (48 - 1) * 60 * 1000).getTime());

        console.log(`常规预测(day_${daysSinceSolarTerm}) 第${day+1}天: 使用${new Date(startTime).toISOString()} - ${new Date(sourceEndTimes[day]).toISOString()}的48条1分钟数据`);
      }
    }

    console.log(`预测目标时间段: ${new Date(targetStartTime).toISOString()} - ${new Date(targetEndTime).toISOString()}`);

    // 4. 获取源数据
    try {
      // 存储各段源数据
      const allKlineData: any[][] = [];

      // 获取每段1分钟K线数据
      for (let i = 0; i < sourceStartTimes.length; i++) {
        const klineData = await binanceService.fetchMinuteKline(
          symbol,
          sourceStartTimes[i],
          sourceEndTimes[i],
          sourceMinuteCounts[i]
        );

        if (!klineData || klineData.length < sourceMinuteCounts[i]) {
          console.error(`获取第${i+1}段K线数据失败或数据不足，预期${sourceMinuteCounts[i]}条，实际获取${klineData?.length || 0}条`);
        return null;
        }

        allKlineData.push(klineData);
      }

      // 记录最后一条数据的时间，用于连续性检查
      if (sourceEndTimes.length > 0) {
        const symbolKey = `${symbol}_${solarTermDate}`;
        this.lastPredictionDataEndTime.set(symbolKey, sourceEndTimes[sourceEndTimes.length - 1] + 60 * 1000);
      }

      // 5. 生成预测点
      const targetDurationHours = (targetEndTime - targetStartTime) / (60 * 60 * 1000);
      const predictionData = this.generate30MinPredictionPoints(
        allKlineData,
        targetStartTime,
        targetDurationHours
      );

      if (predictionData.length === 0) {
        console.error('生成预测数据点失败');
        return null;
      }

      // 6. 创建并保存预测结果
      const predictionCycle = predictionPoint.isSolarTermDayStart ? 'solar_term_day_start' : predictionPoint.cycle;

      try {
      // 先检查是否已存在相同时间段的预测
      const existingPrediction = await SolarTermPredictionLine.findOne({
        symbol,
        targetStartTime
      });

      if (existingPrediction) {
        // 更新已有记录
        existingPrediction.predictionData = predictionData;
        existingPrediction.targetEndTime = targetEndTime;
        existingPrediction.predictionTime = date.getTime();
          existingPrediction.predictionCycle = predictionCycle;
          existingPrediction.solarTermDate = solarTermDate;
          existingPrediction.isActive = true;

        await existingPrediction.save();
          console.log(`更新预测: ${solarTermDate} ${predictionCycle}, 时间段: ${new Date(targetStartTime).toISOString()}`);
        return existingPrediction;
        } else {
      // 创建新的预测记录
      const newPrediction = new SolarTermPredictionLine({
        symbol,
        predictionTime: date.getTime(),
        targetStartTime,
        targetEndTime,
        solarTermDate,
            predictionCycle,
        predictionData,
        isActive: true
      });

      await newPrediction.save();
          console.log(`创建预测: ${solarTermDate} ${predictionCycle}, 时间段: ${new Date(targetStartTime).toISOString()}`);
      return newPrediction;
        }
      } catch (error) {
        console.error('保存预测数据失败:', error);
        return null;
      }
    } catch (error) {
      console.error('生成预测失败:', error);
      return null;
    }
  }

  /**
   * 生成30分钟预测点
   * 根据多段1分钟K线数据生成30分钟预测点
   * 每段K线数据对应预测一天
   *
   * @param klineDataSegments 多段1分钟K线数据数组
   * @param targetStartTime 目标起始时间
   * @param targetDurationHours 目标持续小时数
   * @returns 生成的预测点数组
   */
  private generate30MinPredictionPoints(
    klineDataSegments: any[][],
    targetStartTime: number,
    targetDurationHours: number
  ): { time: number, value: number }[] {
    // 预测点数组
    const predictionPoints: { time: number, value: number }[] = [];

    // 计算每个30分钟预测点
    const totalPoints = targetDurationHours * 2; // 每小时2个30分钟点
    const pointsPerDay = 48; // 一天24小时，每小时2个点，共48个点

    for (let i = 0; i < totalPoints; i++) {
      // 计算当前预测点的时间
      const pointTime = targetStartTime + i * 30 * 60 * 1000;

      // 确定这个预测点属于哪一天
      const dayIndex = Math.floor(i / pointsPerDay);

      // 如果数据段不够用，使用最后一段
      const segmentIndex = Math.min(dayIndex, klineDataSegments.length - 1);
      const klineData = klineDataSegments[segmentIndex];

      // 计算在当天内的点索引
      const pointIndexInDay = i % pointsPerDay;

      // 将当天的48个预测点映射到48条K线数据
      // 每个预测点直接对应一条K线
      const klineIndex = Math.floor(pointIndexInDay * klineData.length / pointsPerDay);

        // 使用收盘价作为预测值
      const predictedValue = parseFloat(klineData[klineIndex][4]);

      predictionPoints.push({
        time: pointTime,
        value: predictedValue
      });
    }

    return predictionPoints;
  }

  /**
   * 获取最近的预测数据
   *
   * @param symbol 交易对符号
   * @param limit 限制结果数量
   * @returns 预测数据列表
   */
  public async getRecentPredictions(symbol: string = 'BTCUSDT', limit: number = 300): Promise<any[]> {
    try {
      // 获取最近的预测数据 - 不筛选isActive状态以显示所有数据
      const predictions = await SolarTermPredictionLine.find({ symbol })
        .sort({ predictionTime: -1 })
        .limit(limit);

      if (!predictions || predictions.length === 0) {
        return [];
      }

      // 提取所有预测点并按时间排序
      let allPoints: { time: number, value: number, cycle: string }[] = [];

      predictions.forEach(prediction => {
        prediction.predictionData.forEach((point: { time: number, value: number }) => {
          allPoints.push({
            time: point.time,
            value: point.value,
            // 返回给前端的是cycle字段
            cycle: prediction.predictionCycle
          });
        });
      });

      // 按时间排序
      allPoints.sort((a, b) => a.time - b.time);

      // 去除重复的时间点（保留最近预测的）
      const uniquePointsMap = new Map<number, { time: number, value: number, cycle: string }>();
      allPoints.forEach(point => {
        uniquePointsMap.set(point.time, point);
      });

      // 转换回数组并格式化返回 - 直接返回时间戳而不是ISO字符串
      const uniquePoints = Array.from(uniquePointsMap.values())
        .sort((a, b) => a.time - b.time)
        .map(point => ({
          time: point.time, // 直接返回时间戳
          value: point.value,
          cycle: point.cycle
        }));

      return uniquePoints;
    } catch (error) {
      console.error('获取最近预测数据失败:', error);
      return [];
    }
  }

  /**
   * 检查预测数据完整性
   *
   * @param date 当前日期（默认北京时间）
   * @param symbol 交易对符号
   * @returns 检查结果
   */
  public async checkPredictionIntegrity(date: Date = new Date(Date.now() + 8 * 60 * 60 * 1000), symbol: string = 'BTCUSDT'): Promise<{
    success: boolean;
    message: string;
    missed: number;
    generated: number;
    skipped: number;
  }> {
    console.log(`开始检查预测数据完整性: ${date.toISOString()}`);

    // 获取最近的预测时间点
    const recentTimePoints = this.findRecentPredictionTimePoints(date, 3); // 检查最近3天的数据

    let missed = 0;
    let generated = 0;
    let skipped = 0;

    // 检查每个时间点是否存在预测数据
    for (const timePoint of recentTimePoints) {
      const exists = await this.checkPredictionExists(timePoint, symbol);

      if (!exists) {
        console.log(`缺失预测数据: ${timePoint.toISOString()}`);
        missed++;

        // 生成缺失的预测数据
        const prediction = await this.generatePrediction(timePoint, symbol);

          if (prediction) {
          console.log(`补充生成预测数据: ${timePoint.toISOString()}`);
          generated++;
          } else {
          console.error(`补充生成预测数据失败: ${timePoint.toISOString()}`);
        }
      } else {
        console.log(`预测数据已存在: ${timePoint.toISOString()}`);
        skipped++;
      }
    }

      return {
        success: true,
      message: `检查完成。缺失: ${missed}, 生成: ${generated}, 跳过: ${skipped}`,
      missed,
      generated,
      skipped
    };
  }

  /**
   * 检查指定时间点是否存在预测数据
   *
   * @param date 时间点
   * @param symbol 交易对符号
   * @returns 是否存在预测
   */
  private async checkPredictionExists(date: Date, symbol: string): Promise<boolean> {
    // 使用与isPredictionTimePoint相同的逻辑判断是否是预测时间点
    const predictionPoint = this.isPredictionTimePoint(date);

    // 如果不是预测时间点，视为已存在（不需要补齐）
    if (!predictionPoint) {
      return true;
    }

    // 获取时间点所属的节气周期信息
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();

    // 判断是否是当月第一个节气日
    const isSolarTermDay = isMonthFirstSolarTermDay(year, month, day);

    // 获取当前月和前一个月的节气日信息
    const currentSolarTerm = getMonthFirstSolarTerm(year, month);

    let prevMonthDate = new Date(date);
    prevMonthDate.setMonth(prevMonthDate.getMonth() - 1);
    const prevYear = prevMonthDate.getFullYear();
    const prevMonth = prevMonthDate.getMonth() + 1;
    const prevSolarTerm = getMonthFirstSolarTerm(prevYear, prevMonth);

    // 确定当前时间所属的节气周期（与isPredictionTimePoint逻辑一致）
    let solarTermYear: number;
    let solarTermMonth: number;
    let solarTermDay: number;
    let targetStartTime: number;

    // 创建当月节气日期对象
    const currentSolarTermDate = currentSolarTerm ?
      new Date(year, month - 1, currentSolarTerm.day) : null;
    currentSolarTermDate?.setHours(0, 0, 0, 0);

    // 创建前月节气日期对象
    const prevSolarTermDate = prevSolarTerm ?
      new Date(prevYear, prevMonth - 1, prevSolarTerm.day) : null;
    prevSolarTermDate?.setHours(0, 0, 0, 0);

    const currentTime = date.getTime();

    // 判断当前时间属于哪个节气周期
    if (currentSolarTermDate && currentTime >= currentSolarTermDate.getTime()) {
      // 当前时间 >= 当月节气日，属于当月节气周期
      solarTermYear = year;
      solarTermMonth = month;
      solarTermDay = currentSolarTerm!.day;
    } else if (prevSolarTermDate) {
      // 当前时间 < 当月节气日，属于前月节气周期
      solarTermYear = prevYear;
      solarTermMonth = prevMonth;
      solarTermDay = prevSolarTerm!.day;
    } else {
      // 无法确定节气周期，视为已存在
      return true;
    }

    // 计算目标时间范围
    if (isSolarTermDay && hour === 0) {
      // 节气日当日00:00特殊预测
      const prevDay = new Date(date);
      prevDay.setDate(prevDay.getDate() - 1);
      prevDay.setHours(23, 0, 0, 0);
      targetStartTime = prevDay.getTime();
    } else {
      // 其他所有预测（节气日23:00和节气日后的23:00）
      targetStartTime = date.getTime();
    }

    // 查询数据库检查是否存在
    try {
      const prediction = await SolarTermPredictionLine.findOne({
        symbol,
        targetStartTime
      });

      return !!prediction;
    } catch (error) {
      console.error('检查预测存在性失败:', error);
      return false;
    }
  }

  /**
   * 查找最近的预测时间点
   *
   * @param date 当前日期
   * @param daysToCheck 检查天数
   * @returns 需要检查的预测时间点
   */
  private findRecentPredictionTimePoints(date: Date, daysToCheck: number = 3): Date[] {
    const timePoints: Date[] = [];
    const currentDate = new Date(date);

    // 计算起始检查日期（当前日期往前推N天）
    const startDate = new Date(currentDate);
    startDate.setDate(startDate.getDate() - daysToCheck);
    startDate.setHours(0, 0, 0, 0);

    // 遍历每一天
    const checkDate = new Date(startDate);
    while (checkDate <= currentDate) {
      const year = checkDate.getFullYear();
      const month = checkDate.getMonth() + 1;
      const day = checkDate.getDate();

      // 判断是否是当月第一个节气日
      const isSolarTermDay = isMonthFirstSolarTermDay(year, month, day);

      // 节气日当天0:00特殊预测点
      if (isSolarTermDay) {
        const specialPoint = new Date(checkDate);
        specialPoint.setHours(0, 0, 0, 0);

        // 只添加已经过去的时间点
        if (specialPoint <= currentDate) {
          timePoints.push(specialPoint);
        }
      }

      // 每天23:00的预测点 - 使用isPredictionTimePoint判断是否有效
      const regularPoint = new Date(checkDate);
      regularPoint.setHours(23, 0, 0, 0);

      // 只添加已经过去的时间点，并且是有效的预测时间点
      if (regularPoint <= currentDate) {
        const predictionPoint = this.isPredictionTimePoint(regularPoint);
        if (predictionPoint) {
          timePoints.push(regularPoint);
        }
      }

      // 移到下一天
      checkDate.setDate(checkDate.getDate() + 1);
    }

    return timePoints;
  }

}

export default new SolarTermPredictionService();