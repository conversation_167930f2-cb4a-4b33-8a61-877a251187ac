import React, { useState, useEffect, useRef } from 'react';
import { useToast } from '../../../components/ui/use-toast';
import useAdminStore from '../../../store/useAdminStore';
import AdminLayout from '../../../components/admin/AdminLayout';
import { Upload } from 'lucide-react';
import configService from '../../../services/configService';
import { Button, Input, Card } from '../../../components/admin/ui';

const BasicSettings: React.FC = () => {
  const { getSystemSettings, updateSystemSettings } = useAdminStore();
  const { toast } = useToast();

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const logoFileRef = useRef<HTMLInputElement>(null);
  const faviconFileRef = useRef<HTMLInputElement>(null);

  // 初始化表单数据
  const [formData, setFormData] = useState({
    siteInfo: {
      siteName: 'BTC 预测',
      siteDescription: '',
      siteKeywords: '',
      logoUrl: '/logo.png',
      faviconUrl: '/favicon.ico',
      copyright: '',
      socialLinks: {
        twitter: '',
        telegram: '',
        facebook: '',
        instagram: '',
        github: ''
      },
      defaultLanguage: 'zh' as 'zh' | 'en'
    }
  });

  // 获取系统设置
  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      const response = await getSystemSettings();
      setFormData({
        siteInfo: response.settings.siteInfo
      });
      setIsLoading(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取系统设置失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };

  // 初次加载时获取设置
  useEffect(() => {
    fetchSettings();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理表单输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name.includes('.')) {
      // 处理嵌套属性 (如 siteInfo.siteName)
      const parts = name.split('.');

      if (parts.length === 2) {
        const [section, field] = parts;
        setFormData(prev => ({
          ...prev,
          [section]: {
            ...prev[section as keyof typeof prev],
            [field]: value
          }
        }));
      } else if (parts.length === 3) {
        // 处理更深层嵌套 (如 siteInfo.socialLinks.twitter)
        const [section, subsection, field] = parts;
        setFormData(prev => {
          const sectionObj = prev[section as keyof typeof prev] as any;
          return {
            ...prev,
            [section]: {
              ...sectionObj,
              [subsection]: {
                ...sectionObj[subsection],
                [field]: value
              }
            }
          };
        });
      }
    } else {
      // 处理普通属性
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // 保存设置
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSaving(true);
      await updateSystemSettings({
        siteInfo: formData.siteInfo
      });
      toast({
        title: '基础设置已成功更新',
        variant: 'default'
      });
      setIsSaving(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新基础设置失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsSaving(false);
    }
  };

  // 处理Logo上传
  const handleLogoUpload = () => {
    logoFileRef.current?.click();
  };

  // 处理Favicon上传
  const handleFaviconUpload = () => {
    faviconFileRef.current?.click();
  };

  // 处理文件选择
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>, type: 'logo' | 'favicon') => {
    const file = e.target.files?.[0];
    if (!file) return;

    // 创建FormData对象
    const formData = new FormData();
    formData.append(type, file);

    try {
      // 获取API基础URL
      const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000/api';

      // 获取管理员token
      const adminStorage = localStorage.getItem('admin-storage');
      let token = '';

      if (adminStorage) {
        try {
          const parsedStorage = JSON.parse(adminStorage);
          token = parsedStorage.state?.token || '';
        } catch (error) {
          console.error('解析token时出错:', error);
        }
      }

      if (!token) {
        toast({
          title: '未找到有效的认证信息，请重新登录',
          variant: 'destructive'
        });
        return;
      }

      // 发送上传请求
      const response = await fetch(`${API_URL}/admin/settings/upload-${type}`, {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`上传失败: ${response.statusText}`);
      }

      const result = await response.json();

      // 更新表单数据
      setFormData(prev => ({
        ...prev,
        siteInfo: {
          ...prev.siteInfo,
          [`${type}Url`]: result[`${type}Url`]
        }
      }));

      toast({
        title: `${type === 'logo' ? 'Logo' : 'Favicon'} 上传成功`,
        variant: 'default'
      });

      // 强制刷新配置缓存，确保前端显示最新的logo
      console.log(`${type}上传成功，URL:`, result[`${type}Url`]);
      await configService.refreshConfig();

      // 更新本地表单数据，确保预览正确显示
      setFormData(prev => ({
        ...prev,
        siteInfo: {
          ...prev.siteInfo,
          [`${type}Url`]: result[`${type}Url`]
        }
      }));

      // 清除文件输入
      if (type === 'logo' && logoFileRef.current) {
        logoFileRef.current.value = '';
      } else if (type === 'favicon' && faviconFileRef.current) {
        faviconFileRef.current.value = '';
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : `上传${type === 'logo' ? 'Logo' : 'Favicon'}失败`;
      toast({
        title: message,
        variant: 'destructive'
      });
    }
  };

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">基础设置</h1>
          <p className="text-gray-600 mt-1">管理网站基本信息和配置</p>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <Card>
            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 左侧表单 */}
                <div className="space-y-4">
                  <div className="mb-4">
                    <Input
                      label="网站名称"
                      id="siteName"
                      name="siteInfo.siteName"
                      value={formData.siteInfo.siteName}
                      onChange={handleChange}
                    />
                  </div>

                  <div className="mb-4">
                    <Input
                      label="网站描述"
                      id="siteDescription"
                      name="siteInfo.siteDescription"
                      value={formData.siteInfo.siteDescription}
                      onChange={handleChange}
                    />
                  </div>

                  <div className="mb-4">
                    <Input
                      label="网站关键词"
                      id="siteKeywords"
                      name="siteInfo.siteKeywords"
                      value={formData.siteInfo.siteKeywords}
                      onChange={handleChange}
                      placeholder="用逗号分隔关键词"
                    />
                  </div>

                  <div className="mb-4">
                    <Input
                      label="版权信息"
                      id="copyright"
                      name="siteInfo.copyright"
                      value={formData.siteInfo.copyright}
                      onChange={handleChange}
                      placeholder="© 2024 网站名称. 保留所有权利."
                    />
                  </div>

                  <div className="mb-4">
                    <label htmlFor="defaultLanguage" className="block text-sm font-medium text-gray-700 mb-1">
                      默认语言
                    </label>
                    <select
                      id="defaultLanguage"
                      name="siteInfo.defaultLanguage"
                      value={formData.siteInfo.defaultLanguage}
                      onChange={handleChange}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="zh">中文</option>
                      <option value="en">英文</option>
                    </select>
                  </div>
                </div>

                {/* 右侧表单 */}
                <div className="space-y-6">
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Logo
                    </label>
                    <div className="flex items-center gap-4">
                      <div className="h-20 w-20 border rounded flex items-center justify-center overflow-hidden bg-white">
                        {formData.siteInfo.logoUrl ? (
                          <img
                            src={formData.siteInfo.logoUrl.startsWith('http')
                              ? formData.siteInfo.logoUrl
                              : formData.siteInfo.logoUrl.startsWith('/uploads')
                                ? `${process.env.REACT_APP_API_URL || 'http://localhost:4000'}${formData.siteInfo.logoUrl}`
                                : formData.siteInfo.logoUrl}
                            alt="Logo"
                            className="max-h-full max-w-full object-contain"
                            onError={(e) => {
                              console.error('Logo预览加载失败:', e, formData.siteInfo.logoUrl);
                              const target = e.target as HTMLImageElement;
                              target.onerror = null; // 防止无限循环
                              target.style.display = 'none'; // 隐藏图片
                            }}
                          />
                        ) : (
                          <span className="text-gray-400 text-xs">无Logo</span>
                        )}
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleLogoUpload}
                        className="flex items-center gap-2"
                      >
                        <Upload className="h-4 w-4" />
                        <span>上传Logo</span>
                      </Button>
                      <input
                        type="file"
                        ref={logoFileRef}
                        className="hidden"
                        accept="image/*"
                        onChange={(e) => handleFileChange(e, 'logo')}
                      />
                    </div>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Favicon
                    </label>
                    <div className="flex items-center gap-4">
                      <div className="h-10 w-10 border rounded flex items-center justify-center overflow-hidden bg-white">
                        {formData.siteInfo.faviconUrl ? (
                          <img
                            src={formData.siteInfo.faviconUrl.startsWith('http')
                              ? formData.siteInfo.faviconUrl
                              : formData.siteInfo.faviconUrl.startsWith('/uploads')
                                ? `${process.env.REACT_APP_API_URL || 'http://localhost:4000'}${formData.siteInfo.faviconUrl}`
                                : formData.siteInfo.faviconUrl}
                            alt="Favicon"
                            className="max-h-full max-w-full object-contain"
                            onError={(e) => {
                              console.error('Favicon预览加载失败:', e, formData.siteInfo.faviconUrl);
                              const target = e.target as HTMLImageElement;
                              target.onerror = null; // 防止无限循环
                              target.style.display = 'none'; // 隐藏图片
                            }}
                          />
                        ) : (
                          <span className="text-gray-400 text-xs">无图标</span>
                        )}
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleFaviconUpload}
                        className="flex items-center gap-2"
                      >
                        <Upload className="h-4 w-4" />
                        <span>上传Favicon</span>
                      </Button>
                      <input
                        type="file"
                        ref={faviconFileRef}
                        className="hidden"
                        accept="image/*"
                        onChange={(e) => handleFileChange(e, 'favicon')}
                      />
                    </div>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      社交媒体链接
                    </label>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <label htmlFor="twitter" className="w-20 text-sm text-gray-600">Twitter</label>
                        <Input
                          id="twitter"
                          name="siteInfo.socialLinks.twitter"
                          value={formData.siteInfo.socialLinks.twitter}
                          onChange={handleChange}
                          placeholder="https://twitter.com/youraccount"
                          className="flex-1"
                        />
                      </div>
                      <div className="flex items-center gap-2">
                        <label htmlFor="telegram" className="w-20 text-sm text-gray-600">Telegram</label>
                        <Input
                          id="telegram"
                          name="siteInfo.socialLinks.telegram"
                          value={formData.siteInfo.socialLinks.telegram}
                          onChange={handleChange}
                          placeholder="https://t.me/yourchannel"
                          className="flex-1"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end mt-6">
                <Button
                  type="submit"
                  disabled={isSaving}
                  loading={isSaving}
                >
                  {isSaving ? '保存中...' : '保存设置'}
                </Button>
              </div>
            </form>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
};

export default BasicSettings;
