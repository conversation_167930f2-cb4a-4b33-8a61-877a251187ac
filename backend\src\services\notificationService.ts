import mongoose from 'mongoose';
import Notification, { NotificationType, NotificationPriority } from '../models/Notification';
import User from '../models/User';

/**
 * 通知服务类
 * 负责创建、管理和发送用户通知
 */
class NotificationService {
  
  /**
   * 创建通知
   * @param userId 用户ID
   * @param type 通知类型
   * @param title 通知标题
   * @param content 通知内容
   * @param priority 优先级
   * @param metadata 额外数据
   */
  async createNotification(
    userId: mongoose.Types.ObjectId,
    type: NotificationType,
    title: string,
    content: string,
    priority: NotificationPriority = 'medium',
    metadata?: any
  ) {
    try {
      const notification = await Notification.create({
        userId,
        type,
        title,
        content,
        priority,
        metadata,
        isRead: false
      });

      console.log(`[通知服务] 为用户 ${userId} 创建通知: ${type} - ${title}`);
      return notification;
    } catch (error) {
      console.error('[通知服务] 创建通知失败:', error);
      throw error;
    }
  }

  /**
   * 注册成功获得试用通知
   * @param userId 用户ID
   * @param trialDays 试用天数
   */
  async createTrialGrantedNotification(userId: mongoose.Types.ObjectId, trialDays: number) {
    return this.createNotification(
      userId,
      'trial_granted',
      '🎉 欢迎加入！',
      `恭喜您成功注册！我们为您提供了 ${trialDays} 天的免费试用期，您可以体验所有高级功能。`,
      'medium',
      { trialDays }
    );
  }

  /**
   * 试用期即将到期通知
   * @param userId 用户ID
   * @param daysRemaining 剩余天数
   */
  async createTrialExpiringNotification(userId: mongoose.Types.ObjectId, daysRemaining: number) {
    return this.createNotification(
      userId,
      'trial_expiring',
      '⏰ 试用期即将到期',
      `您的试用期将在 ${daysRemaining} 天后到期。为了继续享受我们的服务，请考虑升级我们的付费计划。`,
      'high',
      { daysRemaining }
    );
  }

  /**
   * 订阅成功通知
   * @param userId 用户ID
   * @param plan 订阅计划
   * @param endDate 订阅结束日期
   */
  async createSubscriptionSuccessNotification(
    userId: mongoose.Types.ObjectId, 
    plan: string, 
    endDate: Date
  ) {
    const planNames = {
      monthly: '月度',
      quarterly: '季度',
      yearly: '年度'
    };
    
    const planName = planNames[plan as keyof typeof planNames] || plan;
    
    return this.createNotification(
      userId,
      'subscription_success',
      '✅ 订阅成功！',
      `感谢您升级我们的${planName}计划！您现在可以享受所有高级功能，有效期至 ${endDate.toLocaleDateString()}。`,
      'high',
      { plan, endDate }
    );
  }

  /**
   * 订阅即将到期通知
   * @param userId 用户ID
   * @param plan 订阅计划
   * @param daysRemaining 剩余天数
   */
  async createSubscriptionExpiringNotification(
    userId: mongoose.Types.ObjectId,
    plan: string,
    daysRemaining: number
  ) {
    const planNames = {
      monthly: '月度',
      quarterly: '季度', 
      yearly: '年度'
    };
    
    const planName = planNames[plan as keyof typeof planNames] || plan;
    
    return this.createNotification(
      userId,
      'subscription_expiring',
      '🔔 订阅即将到期',
      `您的${planName}订阅将在 ${daysRemaining} 天后到期。请及时续费以继续享受我们的服务。`,
      'high',
      { plan, daysRemaining }
    );
  }

  /**
   * 邀请奖励通知
   * @param userId 用户ID
   * @param rewardDays 奖励天数
   * @param inviteCount 邀请人数
   */
  async createInviteRewardNotification(
    userId: mongoose.Types.ObjectId,
    rewardDays: number,
    inviteCount: number
  ) {
    return this.createNotification(
      userId,
      'invite_reward',
      '🎁 邀请奖励到账！',
      `恭喜您！成功邀请 ${inviteCount} 位朋友注册并验证邮箱，获得 ${rewardDays} 天试用期奖励。感谢您的推荐！`,
      'medium',
      { rewardDays, inviteCount }
    );
  }

  /**
   * 获取用户通知列表
   * @param userId 用户ID
   * @param page 页码
   * @param limit 每页数量
   * @param unreadOnly 是否只获取未读通知
   */
  async getUserNotifications(
    userId: mongoose.Types.ObjectId,
    page: number = 1,
    limit: number = 20,
    unreadOnly: boolean = false
  ) {
    try {
      const skip = (page - 1) * limit;
      const filter: any = { userId };
      
      if (unreadOnly) {
        filter.isRead = false;
      }

      const [notifications, total] = await Promise.all([
        Notification.find(filter)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        Notification.countDocuments(filter)
      ]);

      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('[通知服务] 获取用户通知失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户未读通知数量
   * @param userId 用户ID
   */
  async getUnreadCount(userId: mongoose.Types.ObjectId) {
    try {
      return await Notification.countDocuments({ userId, isRead: false });
    } catch (error) {
      console.error('[通知服务] 获取未读通知数量失败:', error);
      throw error;
    }
  }

  /**
   * 标记通知为已读
   * @param notificationId 通知ID
   * @param userId 用户ID（用于权限验证）
   */
  async markAsRead(notificationId: string, userId: mongoose.Types.ObjectId) {
    try {
      const notification = await Notification.findOneAndUpdate(
        { _id: notificationId, userId },
        { isRead: true, readAt: new Date() },
        { new: true }
      );

      if (!notification) {
        throw new Error('通知不存在或无权限访问');
      }

      return notification;
    } catch (error) {
      console.error('[通知服务] 标记通知已读失败:', error);
      throw error;
    }
  }

  /**
   * 标记所有通知为已读
   * @param userId 用户ID
   */
  async markAllAsRead(userId: mongoose.Types.ObjectId) {
    try {
      const result = await Notification.updateMany(
        { userId, isRead: false },
        { isRead: true, readAt: new Date() }
      );

      console.log(`[通知服务] 为用户 ${userId} 标记 ${result.modifiedCount} 条通知为已读`);
      return result;
    } catch (error) {
      console.error('[通知服务] 标记所有通知已读失败:', error);
      throw error;
    }
  }

  /**
   * 删除通知
   * @param notificationId 通知ID
   * @param userId 用户ID（用于权限验证）
   */
  async deleteNotification(notificationId: string, userId: mongoose.Types.ObjectId) {
    try {
      const notification = await Notification.findOneAndDelete({
        _id: notificationId,
        userId
      });

      if (!notification) {
        throw new Error('通知不存在或无权限访问');
      }

      return notification;
    } catch (error) {
      console.error('[通知服务] 删除通知失败:', error);
      throw error;
    }
  }

  /**
   * 清理用户的旧通知（保留最近30天）
   * @param userId 用户ID
   * @param daysToKeep 保留天数
   */
  async cleanupOldNotifications(userId: mongoose.Types.ObjectId, daysToKeep: number = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await Notification.deleteMany({
        userId,
        createdAt: { $lt: cutoffDate }
      });

      console.log(`[通知服务] 为用户 ${userId} 清理了 ${result.deletedCount} 条旧通知`);
      return result;
    } catch (error) {
      console.error('[通知服务] 清理旧通知失败:', error);
      throw error;
    }
  }
}

export default new NotificationService();
