/**
 * K线数据获取和管理Hook
 * 
 * 该Hook负责K线数据的获取、更新和历史数据加载
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { fetchCandleData, fetchThirtyMinKlineData } from '../services/api';
import { KLineData } from '../types/chartTypes';

interface KLineDataState {
  data: KLineData[];
  isLoading: boolean;
  isLoadingMoreCandles: boolean;
  error: string | null;
}

/**
 * K线数据获取和管理Hook
 * @param refreshInterval 数据刷新间隔(毫秒)
 * @returns K线数据状态和操作方法
 */
export const useKLineData = (refreshInterval: number = 10000) => {
  // 状态管理
  const [state, setState] = useState<KLineDataState>({
    data: [],
    isLoading: true,
    isLoadingMoreCandles: false,
    error: null
  });

  // 引用存储
  const isRefreshingRef = useRef<boolean>(false);

  // 初始加载K线数据
  const loadData = useCallback(async () => {
    if (isRefreshingRef.current) return;
    
    isRefreshingRef.current = true;
    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      const candleResponse = await fetchCandleData();
      if (candleResponse && Array.isArray(candleResponse) && candleResponse.length > 0) {
        console.log(`获取到 ${candleResponse.length} 条K线数据`);
        setState(prev => ({ 
          ...prev, 
          data: candleResponse,
          error: null
        }));
      } else {
        console.warn('获取到的K线数据为空或无效');
      }
    } catch (err) {
      console.error('数据加载失败:', err);
      setState(prev => ({ 
        ...prev, 
        error: '获取K线数据失败' 
      }));
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
      isRefreshingRef.current = false;
    }
  }, []);

  // 刷新K线数据
  const refreshData = useCallback(async () => {
    if (isRefreshingRef.current) return;
    
    isRefreshingRef.current = true;
    
    try {
      const candleResponse = await fetchCandleData();
      if (candleResponse && Array.isArray(candleResponse) && candleResponse.length > 0) {
        console.log(`刷新获取到 ${candleResponse.length} 条K线数据`);
        
        // 合并已加载的历史数据与新获取的数据
        setState(prev => {
          // 创建一个Map用于去重，保持已有数据
          const uniqueMap = new Map();
          
          // 先处理当前所有数据（保留历史数据）
          prev.data.forEach(item => {
            const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
            uniqueMap.set(timeKey, item);
          });
          
          // 再处理新数据，只有新数据会覆盖旧数据中的同时间点
          candleResponse.forEach(item => {
            const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
            uniqueMap.set(timeKey, item);
          });
          
          // 转换为数组并排序
          const combinedData = Array.from(uniqueMap.values()) as KLineData[];
          combinedData.sort((a, b) => {
            const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
            const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
            return timeA - timeB;
          });
          
          console.log(`定时刷新合并后共有 ${combinedData.length} 条K线数据（保留了历史数据）`);
          return { 
            ...prev, 
            data: combinedData,
            error: null
          };
        });
      }
    } catch (err) {
      console.error('定时刷新K线数据失败:', err);
    } finally {
      isRefreshingRef.current = false;
    }
  }, []);

  // 加载更多历史K线数据
  const loadMoreHistory = useCallback(async (oldestTime: number) => {
    setState(prev => {
      if (prev.isLoadingMoreCandles) return prev;
      return { ...prev, isLoadingMoreCandles: true };
    });
    
    console.log(`加载更早的30分钟K线数据，时间戳: ${oldestTime}, ${new Date(oldestTime * 1000).toISOString()}`);
    
    try {
      // 获取指定时间之前的30分钟K线数据
      // 后端API需要毫秒格式的时间戳，而图表传来的oldestTime是秒级时间戳
      const olderCandles = await fetchThirtyMinKlineData(oldestTime * 1000);
      
      if (olderCandles && Array.isArray(olderCandles) && olderCandles.length > 0) {
        console.log(`获取到 ${olderCandles.length} 条更早的30分钟K线数据`);
        
        // 合并新旧数据并去重
        setState(prev => {
          // 创建一个Map用于去重
          const uniqueMap = new Map();
          
          // 先处理当前数据
          prev.data.forEach(item => {
            const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
            uniqueMap.set(timeKey, item);
          });
          
          // 再处理新数据，有相同时间则新数据覆盖旧数据
          olderCandles.forEach(item => {
            const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
            uniqueMap.set(timeKey, item);
          });
          
          // 转换为数组并排序
          const combinedData = Array.from(uniqueMap.values()) as KLineData[];
          combinedData.sort((a, b) => {
            const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
            const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
            return timeA - timeB;
          });
          
          console.log(`合并后共有 ${combinedData.length} 条30分钟K线数据`);
          return { 
            ...prev, 
            data: combinedData,
            isLoadingMoreCandles: false 
          };
        });
      } else {
        console.log('没有更多历史30分钟K线数据了');
        setState(prev => ({ ...prev, isLoadingMoreCandles: false }));
      }
    } catch (err) {
      console.error('加载历史30分钟K线数据失败:', err);
      setState(prev => ({ ...prev, isLoadingMoreCandles: false }));
    }
  }, []);

  // 首次加载和定时刷新
  useEffect(() => {
    // 首次加载数据
    loadData();

    // 设置定时刷新
    const interval = setInterval(refreshData, refreshInterval);

    // 清理定时器
    return () => {
      clearInterval(interval);
    };
  }, [loadData, refreshData, refreshInterval]);

  return {
    candleData: state.data,
    isLoading: state.isLoading,
    isLoadingMoreCandles: state.isLoadingMoreCandles,
    error: state.error,
    loadData,
    refreshData,
    loadMoreHistory
  };
}; 