import { Request, Response } from 'express';
import solarTermPredictionService from '../services/solarTermPredictionService';

/**
 * 节气预测折线控制器
 * 处理与节气预测折线相关的请求
 */
class SolarTermPredictionController {
  /**
   * 获取最近的节气预测折线数据
   * 
   * @param req Express请求对象
   * @param res Express响应对象
   */
  async getRecentPredictions(req: Request, res: Response): Promise<void> {
    try {
      const symbol = req.query.symbol as string || 'BTCUSDT';
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 300;
      
      
      const predictions = await solarTermPredictionService.getRecentPredictions(symbol, limit);
      res.status(200).json(predictions);
    } catch (error) {
      console.error('获取节气预测折线数据失败:', error);
      res.status(500).json({ message: '获取节气预测折线数据失败', error: (error as Error).message });
    }
  }
  
  /**
   * 手动触发节气预测折线生成
   * 仅用于测试和调试
   * 
   * @param req Express请求对象
   * @param res Express响应对象
   */
  async triggerPrediction(req: Request, res: Response): Promise<void> {
    try {
      const symbol = req.body.symbol || 'BTCUSDT';
      // 允许通过body传入特定时间进行测试
      const timestamp = req.body.timestamp ? parseInt(req.body.timestamp) : Date.now();
      const testDate = new Date(timestamp);
      
      console.log(`手动触发节气预测折线生成: ${symbol}, 时间: ${testDate.toISOString()}`);
      
      // 先检查是否是有效的预测时间点
      const predictionPoint = solarTermPredictionService.isPredictionTimePoint(testDate);
      if (!predictionPoint) {
        res.status(400).json({ 
          message: '当前时间不是有效的预测时间点',
          time: testDate.toISOString()
        });
        return;
      }
      
      const prediction = await solarTermPredictionService.generatePrediction(testDate, symbol);
      
      if (prediction) {
        res.status(200).json({
          message: '节气预测折线生成成功',
          prediction: {
            _id: prediction._id,
            symbol: prediction.symbol,
            predictionTime: prediction.predictionTime,
            targetStartTime: prediction.targetStartTime,
            targetEndTime: prediction.targetEndTime,
            solarTermDate: prediction.solarTermDate,
            predictionCycle: prediction.predictionCycle,
            dataPointsCount: prediction.predictionData.length
          }
        });
      } else {
        res.status(400).json({ 
          message: '预测生成失败',
          time: testDate.toISOString()
        });
      }
    } catch (error) {
      console.error('触发节气预测折线生成失败:', error);
      res.status(500).json({ message: '触发节气预测折线生成失败', error: (error as Error).message });
    }
  }
  
  /**
   * 触发预测折线完整性检查
   * 
   * @param req Express请求对象
   * @param res Express响应对象
   */
  async checkPredictionIntegrity(req: Request, res: Response): Promise<void> {
    try {
      const symbol = req.body.symbol || 'BTCUSDT';
      
      console.log(`手动触发预测折线完整性检查: ${symbol}`);
      
      const result = await solarTermPredictionService.checkPredictionIntegrity(new Date(), symbol);
      
      // 如果service没有返回skipped字段，添加默认值0
      const responseResult = {
        ...result,
        skipped: result.skipped !== undefined ? result.skipped : 0
      };
      
      res.status(200).json({
        message: '预测折线完整性检查执行完成',
        result: responseResult
      });
    } catch (error) {
      console.error('预测折线完整性检查失败:', error);
      res.status(500).json({ message: '预测折线完整性检查失败', error: (error as Error).message });
    }
  }
}

export default new SolarTermPredictionController(); 