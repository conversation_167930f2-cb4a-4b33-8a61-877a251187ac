{"name": "backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon --watch 'src/**/*.ts' --exec 'ts-node' src/index.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/multer": "^1.4.12", "axios": "^1.6.0", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "mongodb": "^6.15.0", "mongoose": "^8.0.0", "multer": "^2.0.0", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "nodemailer": "^7.0.3", "socks-proxy-agent": "^8.0.5", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.16", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.9.0", "@types/nodemailer": "^6.4.17", "@types/redis": "^4.0.10", "@types/uuid": "^10.0.0", "@types/winston": "^2.4.4", "cors": "^2.8.5", "express": "^5.1.0", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.8.3"}}