import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from './ui/button';
import { getSubscriptionInfo, getPaymentHistory, SubscriptionInfo as SubscriptionInfoType, PaymentHistory } from '../api/subscription';
import { Badge } from './ui/badge';
import { CreditCard, Clock, Calendar, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

// 订阅信息组件
const SubscriptionInfo: React.FC = () => {
  const [subscription, setSubscription] = useState<SubscriptionInfoType | null>(null);
  const [payments, setPayments] = useState<PaymentHistory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  // 获取订阅信息和支付历史
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // 并行请求数据
        const [subscriptionResponse, paymentsResponse] = await Promise.all([
          getSubscriptionInfo(),
          getPaymentHistory()
        ]);
        
        setSubscription(subscriptionResponse.subscription);
        setPayments(paymentsResponse.payments);
      } catch (error) {
        console.error('获取订阅数据失败:', error);
        setError('获取订阅数据时出错，请稍后重试');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, []);

  // 状态对应的颜色和文案
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'active':
        return { label: '已激活', color: 'bg-success/20 text-success' };
      case 'trial':
        return { label: '试用期', color: 'bg-warning/20 text-warning' };
      case 'finished':
        return { label: '已完成', color: 'bg-success/20 text-success' };
      case 'pending':
        return { label: '处理中', color: 'bg-warning/20 text-warning' };
      case 'failed':
        return { label: '失败', color: 'bg-error/20 text-error' };
      case 'expired':
        return { label: '已过期', color: 'bg-error/20 text-error' };
      default:
        return { label: '未订阅', color: 'bg-secondary/20 text-secondary' };
    }
  };

  // 日期格式化工具
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return '无';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // 订阅类型转中文
  const getPlanName = (type: string | null | undefined) => {
    switch (type) {
      case 'monthly':
        return '月度订阅';
      case 'quarterly':
        return '季度订阅';
      case 'yearly':
        return '年度订阅';
      case 'trial':
        return '试用期';
      default:
        return '无';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">

      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-error/10 text-error rounded-md">
        <AlertCircle className="h-5 w-5 mb-2" />
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 订阅状态卡片 */}
      <div className="border border-border rounded-lg p-5 bg-card">
        <div className="flex justify-between items-start">
          <div className="w-full">
            <h3 className="text-lg font-semibold mb-4">订阅状态</h3>
            
            <dl className="flex flex-wrap justify-between items-center">
              <div className="flex flex-col">
                <dt className="text-sm text-content-secondary mb-1 flex items-center">
                  <Clock className="w-4 h-4 mr-1" />状态
                </dt>
                <dd>
                  <Badge 
                    className={getStatusInfo(subscription?.status || 'inactive').color}
                  >
                    {getStatusInfo(subscription?.status || 'inactive').label}
                  </Badge>
                </dd>
              </div>
              
              <div className="flex flex-col">
                <dt className="text-sm text-content-secondary mb-1 flex items-center">
                  <CreditCard className="w-4 h-4 mr-1" />订阅类型
                </dt>
                <dd className="font-medium">{getPlanName(subscription?.type)}</dd>
              </div>
              
              <div className="flex flex-col">
                <dt className="text-sm text-content-secondary mb-1 flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />到期日期
                </dt>
                <dd className="font-medium">{formatDate(subscription?.endDate)}</dd>
              </div>
              
              <div className="flex flex-col">
                <dt className="text-sm text-content-secondary mb-1 flex items-center">
                  <Clock className="w-4 h-4 mr-1" />剩余天数
                </dt>
                <dd className="font-medium">
                  {subscription?.daysRemaining ? `${subscription.daysRemaining} 天` : '0 天'}
                </dd>
              </div>
            </dl>
          </div>
        </div>
        
        {subscription?.status !== 'active' && (
          <div className="mt-5 pt-5 border-t border-border">
            <Button 
              className="w-full sm:w-auto" 
              onClick={() => navigate('/subscribe')}
            >
              {subscription?.status === 'trial' ? '立即升级' : '开始订阅'}
            </Button>
          </div>
        )}
      </div>
      
      {/* 支付历史 */}
      <div className="border border-border rounded-lg overflow-hidden">
        <div className="p-5 bg-card">
          <h3 className="text-lg font-semibold">支付历史</h3>
        </div>
        
        {payments.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-muted/50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-content-secondary whitespace-nowrap">订阅计划</th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-content-secondary whitespace-nowrap">金额</th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-content-secondary whitespace-nowrap">状态</th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-content-secondary whitespace-nowrap">日期</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-border">
                {payments.map((payment) => (
                  <tr key={payment.id}>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      {getPlanName(payment.plan)}
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      ${payment.amount}
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      <Badge 
                        className={getStatusInfo(payment.status).color}
                      >
                        {getStatusInfo(payment.status).label}
                      </Badge>
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      {new Date(payment.createdAt).toLocaleDateString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="p-5 text-center text-content-secondary">
            暂无支付记录
          </div>
        )}
      </div>
    </div>
  );
};

export default SubscriptionInfo; 