import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import useUserStore from '../store/useUserStore';
import InviteCodesList from '../components/InviteCodesList';
import { User, Lock, Gift, Bell, CreditCard, MessageSquare } from 'lucide-react';

import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { cn } from '../lib/utils';
import SubscriptionInfo from '../components/SubscriptionInfo';
import UserFeedback from '../components/feedback/UserFeedback';
import NotificationList from '../components/NotificationList';
import notificationService from '../services/notificationService';

// 用户中心页面
const UserCenter: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user, error, changePassword } = useUserStore();
  const [activeTab, setActiveTab] = useState('account');
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  // 加载未读通知数量
  const loadUnreadCount = async () => {
    try {
      const count = await notificationService.getUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('加载未读通知数量失败:', error);
      setUnreadCount(0);
    }
  };

  // 组件挂载时加载未读通知数量
  useEffect(() => {
    loadUnreadCount();
    // 每30秒刷新一次未读数量
    const interval = setInterval(loadUnreadCount, 30000);
    return () => clearInterval(interval);
  }, []);

  // 日期格式化工具函数
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 处理密码更改
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordError(null);
    setPasswordSuccess(null);

    if (newPassword !== confirmPassword) {
      setPasswordError('两次输入的密码不一致');
      return;
    }

    if (newPassword.length < 6) {
      setPasswordError('新密码长度必须至少为6个字符');
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await changePassword(oldPassword, newPassword);

      // 如果需要重新认证，显示成功消息并跳转到登录页
      if (response && response.requireReauth) {
        setPasswordSuccess('密码修改成功，请重新登录');
        setOldPassword('');
        setNewPassword('');
        setConfirmPassword('');

        // 3秒后跳转到登录页
        setTimeout(() => {
          navigate('/login', {
            state: {
              message: '密码已修改，请使用新密码登录',
              email: user?.email
            }
          });
        }, 3000);
      } else {
        setPasswordSuccess('密码已成功更新');
        setOldPassword('');
        setNewPassword('');
        setConfirmPassword('');
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : '密码更新失败';
      setPasswordError(message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 用户角色显示
  const renderRoleBadge = () => {
    if (!user) return null;

    let badgeClass = 'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium';
    let roleName = '';

    switch (user.role) {
      case 'subscriber':
        badgeClass += ' bg-success/20 text-success';
        roleName = '订阅用户';
        break;
      case 'normal':
        badgeClass += ' bg-primary/20 text-primary';
        roleName = '普通用户';
        break;
      case 'trial':
        badgeClass += ' bg-warning/20 text-warning';
        roleName = '试用用户';
        break;
      default:
        badgeClass += ' bg-secondary/20 text-secondary';
        roleName = '未知角色';
    }

    return <span className={badgeClass}>{roleName}</span>;
  };

  // 定义菜单项
  const menuItems = [
    { id: 'account', label: '账户信息', icon: <User className="w-4 h-4 mr-2" />, badge: null },
    { id: 'password', label: '修改密码', icon: <Lock className="w-4 h-4 mr-2" />, badge: null },
    { id: 'invites', label: '邀请码管理', icon: <Gift className="w-4 h-4 mr-2" />, badge: null },
    { id: 'subscription', label: '我的订阅', icon: <CreditCard className="w-4 h-4 mr-2" />, badge: null },
    { id: 'feedback', label: '我的反馈', icon: <MessageSquare className="w-4 h-4 mr-2" />, badge: null },
    {
      id: 'notifications',
      label: '系统通知',
      icon: <Bell className="w-4 h-4 mr-2" />,
      badge: unreadCount > 0 ? unreadCount : null
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="container max-w-6xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-[240px_1fr]">
          {/* 左侧菜单栏 */}
          <div className="md:col-span-1">
            <Card>
              <CardContent className="p-0">
                <div className="flex flex-col items-center p-6 border-b border-border">
                  <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center text-xl font-semibold text-primary mb-3">
                  {user?.email.charAt(0).toUpperCase() || 'U'}
                </div>
                  <p className="text-sm text-center font-medium truncate max-w-full">{user?.email}</p>
                <div className="mt-2">{renderRoleBadge()}</div>
              </div>

                <nav className="p-2">
                  {menuItems.map(item => (
                    <Button
                      key={item.id}
                      variant={activeTab === item.id ? "default" : "ghost"}
                      size="sm"
                      className={cn(
                        "w-full justify-start mb-1 h-9 relative",
                        activeTab === item.id
                          ? "bg-primary text-primary-foreground"
                          : "text-content-secondary hover:bg-background/10 hover:text-white"
                      )}
                      onClick={() => {
                        setActiveTab(item.id);
                        // 如果点击的是通知菜单，刷新未读数量
                        if (item.id === 'notifications') {
                          loadUnreadCount();
                        }
                      }}
                    >
                      {item.icon}
                      <span className="flex-1 text-left">{item.label}</span>
                      {/* 显示未读通知数量 */}
                      {item.badge && (
                        <span className="ml-auto bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1">
                          {item.badge > 99 ? '99+' : item.badge}
                        </span>
                      )}
                    </Button>
                  ))}
              </nav>
              </CardContent>
            </Card>
            </div>

          {/* 右侧内容区 */}
          <div className="md:col-span-1">
              {/* 账户信息 */}
              {activeTab === 'account' && (
              <Card>
                <CardHeader>
                  <CardTitle>账户信息</CardTitle>
                  <CardDescription>查看您的账户信息及状态</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div className="space-y-1">
                      <Label className="text-content-muted">电子邮箱</Label>
                      <p className="font-medium">{user?.email || 'N/A'}</p>
                      </div>
                    <div className="space-y-1">
                      <Label className="text-content-muted">账户类型</Label>
                      <p className="font-medium">
                          {user?.role === 'subscriber' ? '订阅用户' :
                           user?.role === 'normal' ? '普通用户' : '试用用户'}
                        </p>
                      </div>
                    <div className="space-y-1">
                      <Label className="text-content-muted">账户创建时间</Label>
                      <p className="font-medium">{formatDate(user?.createdAt || '')}</p>
                      </div>
                    <div className="space-y-1">
                      <Label className="text-content-muted">邮箱验证状态</Label>
                      <p className="font-medium">
                          {user?.isVerified ?
                            <span className="text-success">已验证</span> :
                            <span className="text-error">未验证</span>}
                        </p>
                      </div>
                    </div>

                    {user?.role === 'trial' && (
                    <div className="mt-6 p-4 bg-warning/10 rounded-lg border border-warning/20">
                        <h4 className="text-warning font-semibold mb-2">试用信息</h4>
                      <p className="mb-2 text-sm">您当前正在使用试用版本。</p>
                      <p className="flex justify-between text-sm">
                          <span>试用期截止日期:</span>
                        <span className="font-semibold text-warning">{formatDate(user?.trialEndsAt || '')}</span>
                        </p>
                      </div>
                    )}

                    {user?.role === 'subscriber' && (
                    <div className="mt-6 p-4 bg-success/10 rounded-lg border border-success/20">
                        <h4 className="text-success font-semibold mb-2">订阅信息</h4>
                      <p className="text-sm">感谢您的订阅支持！您目前拥有所有高级功能的访问权限。</p>
                      </div>
                    )}

                    {user?.rewardHistory && user.rewardHistory.length > 0 && (
                      <div className="mt-6 p-4 bg-primary/10 rounded-lg border border-primary/20">
                        <h4 className="text-primary font-semibold mb-2">奖励记录</h4>
                        <div className="space-y-3">
                          {user.rewardHistory.map((reward: {
                            type: string;
                            days: number;
                            grantedAt: string;
                            reason: string;
                          }, index: number) => (
                            <div key={index} className="flex justify-between text-sm border-b border-border pb-2 last:border-0 last:pb-0">
                              <div>
                                <p className="font-medium">{reward.reason}</p>
                                <p className="text-content-muted text-xs">{formatDate(reward.grantedAt)}</p>
                              </div>
                              <span className="font-semibold text-success">+{reward.days}天</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                </CardContent>
              </Card>
              )}

              {/* 修改密码 */}
              {activeTab === 'password' && (
              <Card>
                <CardHeader>
                  <CardTitle>修改密码</CardTitle>
                  <CardDescription>更新您的账户密码</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handlePasswordChange}>
                    {passwordError && (
                      <div className="mb-6 p-3 bg-error/10 border border-error/20 text-error rounded-md text-sm">
                        {passwordError}
                      </div>
                    )}

                    {passwordSuccess && (
                      <div className="mb-6 p-3 bg-success/10 border border-success/20 text-success rounded-md text-sm">
                        {passwordSuccess}
                      </div>
                    )}

                    <div className="space-y-4">
                      <div className="space-y-2 text-left">
                        <Label htmlFor="old-password">当前密码</Label>
                        <Input
                        id="old-password"
                        type="password"
                        value={oldPassword}
                        onChange={(e) => setOldPassword(e.target.value)}
                        required
                      />
                    </div>

                      <div className="space-y-2 text-left">
                        <Label htmlFor="new-password">新密码</Label>
                        <Input
                        id="new-password"
                        type="password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        minLength={6}
                        required
                      />
                    </div>

                      <div className="space-y-2 text-left">
                        <Label htmlFor="confirm-password">确认新密码</Label>
                        <Input
                        id="confirm-password"
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        minLength={6}
                        required
                      />
                      </div>
                    </div>

                    <Button
                      type="submit"
                      className="w-full mt-6"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? '更新中...' : '更新密码'}
                    </Button>
                  </form>
                </CardContent>
              </Card>
              )}

              {/* 邀请码管理 */}
              {activeTab === 'invites' && (
              <Card>
                <CardHeader>
                  <CardTitle>邀请码管理</CardTitle>
                  <CardDescription>管理和使用您的邀请码</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="mb-4 text-sm">
                      您可以使用以下邀请码邀请朋友加入我们。每个邀请码只能使用一次。
                    </p>

                  <div className="mb-6 p-4 bg-primary/10 rounded-lg border border-primary/20 text-sm">
                      <h4 className="text-primary font-semibold mb-2">如何邀请朋友？</h4>
                    <ul className="list-disc list-inside text-content-secondary space-y-1.5">
                        <li>将邀请码发送给您的朋友</li>
                        <li>朋友在注册页面使用邀请码注册</li>
                        <li>朋友验证邮箱后，将计入您的邀请统计</li>
                        <li>成功邀请5位朋友并验证邮箱后，您将获得额外试用天数奖励</li>
                      </ul>
                    </div>

                  <div className="mt-4">
                      <InviteCodesList />
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 我的订阅 */}
            {activeTab === 'subscription' && (
              <Card>
                <CardHeader>
                </CardHeader>
                <CardContent>
                  <SubscriptionInfo />
                </CardContent>
              </Card>
            )}

            {/* 我的反馈 */}
            {activeTab === 'feedback' && (
              <Card>
                <CardHeader>
                  <CardTitle> </CardTitle>
                </CardHeader>
                <CardContent>
                  <UserFeedback />
                </CardContent>
              </Card>
            )}

            {/* 系统通知 */}
            {activeTab === 'notifications' && (
              <Card>
                <CardHeader>
                  <CardTitle>系统通知</CardTitle>
                  <CardDescription>查看您的系统通知</CardDescription>
                </CardHeader>
                <CardContent>
                  <NotificationList />
                </CardContent>
              </Card>
              )}
            </div>
          </div>
        </div>
    </div>
  );
};

export default UserCenter;