import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 导入翻译文件
import translationEN from './locales/en.json';
import translationZH from './locales/zh.json';

// 配置i18next
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: translationEN
      },
      zh: {
        translation: translationZH
      }
    },
    fallbackLng: 'en',
    debug: false, // 关闭调试模式
    interpolation: {
      escapeValue: false // React已经安全地处理了转义
    },
    saveMissing: false, // 禁止保存缺失的键
    missingKeyHandler: () => {} // 空函数处理缺失的键，不输出警告
  });

export default i18n; 