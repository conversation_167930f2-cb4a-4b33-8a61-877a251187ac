import express from 'express';
import adminController from '../controllers/adminController';
import announcementController from '../controllers/announcementController';
import systemSettingsController from '../controllers/systemSettingsController';
import paymentAdminController from '../controllers/paymentAdminController';
import adminFeedbackRoutes from './adminFeedbackRoutes';
import authenticate, { isAdmin } from '../middlewares/authMiddleware';

const router = express.Router();

// 管理员登录
router.post('/login', adminController.adminLogin);

// 所有管理员API需要先经过认证和权限验证
router.use(authenticate);
router.use(isAdmin);

// 用户管理
router.get('/users', adminController.getUsers);
router.post('/users', adminController.createUser);
router.patch('/users/:id', adminController.updateUserRole);
router.delete('/users/:id', adminController.deleteUser);
router.post('/users/:id/ban', adminController.banUser);
router.post('/users/:id/unban', adminController.unbanUser);
router.patch('/users/:id/subscription', adminController.updateUserSubscription);

// 系统设置管理
router.get('/settings', systemSettingsController.getSystemSettings);
router.put('/settings', systemSettingsController.updateSystemSettings);
router.post('/settings/upload-logo', systemSettingsController.uploadLogo);
router.post('/settings/upload-favicon', systemSettingsController.uploadFavicon);

// 支付管理
router.get('/payments', paymentAdminController.getPayments);
router.get('/payments/stats', paymentAdminController.getPaymentStats);
router.get('/payments/:id', paymentAdminController.getPaymentDetails);
router.patch('/payments/:id', paymentAdminController.updatePaymentStatus);

// 公告管理
router.get('/announcements', announcementController.getAnnouncements);
router.post('/announcements', announcementController.createAnnouncement);
router.patch('/announcements/:id', announcementController.updateAnnouncement);
router.delete('/announcements/:id', announcementController.deleteAnnouncement);

// 用户反馈管理
router.use('/feedback', adminFeedbackRoutes);

export default router;