import React, { useEffect, useRef, useCallback, useLayoutEffect } from 'react';
import { IChartApi, ISeriesApi } from 'lightweight-charts';
import { usePredictionLineData } from '../hooks/usePredictionLineData';

// 组件Props定义
interface PredictionLineChartLayerProps {
  chartApi: IChartApi | null;
  visible: boolean;
  refreshInterval?: number;
  predictionSeries?: ISeriesApi<'Area'> | null; // 从图表管理器获取的预测折线系列
}

// 预测折线图层组件
const PredictionLineChartLayer: React.FC<PredictionLineChartLayerProps> = ({
  chartApi,
  visible,
  refreshInterval = 3600000, // 默认1小时刷新一次
  predictionSeries
}) => {
  // 引用存储
  const predictionSeriesRef = useRef<ISeriesApi<'Area'> | null>(null);
  const isMountedRef = useRef(false); // 跟踪组件是否已挂载
  const isDataLoadedRef = useRef(false); // 跟踪数据是否已加载

  // 使用预测数据钩子获取和管理数据
  const { 
    formattedData, 
    isLoading, 
    error 
  } = usePredictionLineData(visible, refreshInterval);

  // 初始化预测折线系列引用
  useEffect(() => {
    // 检查是否提供了预测折线系列
    if (!predictionSeries) {
      console.warn('[预测图层] 未提供预测折线系列实例');
      return;
    }
    
    // 保存引用
      predictionSeriesRef.current = predictionSeries;
    console.log('[预测图层] 使用图表管理器提供的预测折线系列');
    
    // 标记组件已挂载
    isMountedRef.current = true;
  }, [predictionSeries]);

  // 更新预测折线数据可见性
  const updateSeriesVisibility = useCallback(() => {
    if (!predictionSeriesRef.current) return;
    
    try {
      // 通过设置 visible 选项来控制折线的可见性，而不是移除/重建
      predictionSeriesRef.current.applyOptions({
        visible: visible
      });
      
      // 是否显示价格轴
      if (chartApi && predictionSeries) {
        chartApi.priceScale('prediction-line').applyOptions({
          visible: visible
        });
      }
      
      console.log(`[预测图层] 预测折线可见性已更新: ${visible ? '显示' : '隐藏'}`);
    } catch (error) {
      console.error('[预测图层] 更新预测折线可见性失败:', error);
    }
  }, [visible, chartApi, predictionSeries]);

  // 更新预测折线数据
  const updateChartData = useCallback(() => {
    if (!predictionSeriesRef.current || formattedData.length === 0) return;
    
    try {
      // 设置数据
      predictionSeriesRef.current.setData(formattedData);
      isDataLoadedRef.current = true;
      console.log('[预测图层] 当前节气周期预测折线图表数据已更新，共', formattedData.length, '条数据点');
      
      // 已移除fitContent()调用，防止重置用户视图
    } catch (error) {
      console.error('[预测图层] 更新当前节气周期预测折线图表数据失败:', error);
    }
  }, [formattedData, chartApi]);

  // 处理可见性变化 - 只更新可见性
  useEffect(() => {
    if (!isMountedRef.current) return;
    updateSeriesVisibility();
  }, [visible, updateSeriesVisibility]);

  // 数据加载后更新图表
  useEffect(() => {
    if (!isMountedRef.current || !visible || formattedData.length === 0) return;
    updateChartData();
  }, [visible, formattedData, updateChartData]);

  // 错误处理和加载状态监控
  useEffect(() => {
    if (error) {
      console.error('[预测图层] 预测数据加载错误:', error);
    }
    if (isLoading) {
      console.log('[预测图层] 预测数据加载中...');
      }
  }, [isLoading, error]);

  // 组件销毁时的清理
  useEffect(() => {
    return () => {
      // 重置所有状态
      isMountedRef.current = false;
      isDataLoadedRef.current = false;
      console.log('[预测图层] 预测折线图层组件已卸载');
    };
  }, []);

  // 无需渲染实际DOM，这个组件只操作图表
  return null;
};

export default PredictionLineChartLayer; 