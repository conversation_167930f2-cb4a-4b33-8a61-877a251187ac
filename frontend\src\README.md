# 图表组件架构

本文档介绍了重构后的图表组件架构，包括组件拆分、职责分离和使用方法。

## 架构概述

重构后的图表组件架构采用了组件化和模块化的设计理念，将原来庞大的`Chart.tsx`组件（800+行）拆分为多个职责单一的小组件，并提取出通用工具函数。主要包括：

```
frontend/
├── src/
│   ├── components/
│   │   ├── chart/                 # 图表相关组件
│   │   │   ├── ChartCore.tsx      # 图表核心组件 - 负责创建和管理图表实例
│   │   │   ├── MainCandlestickSeries.tsx # 主K线图系列组件
│   │   │   ├── PredictionCandlestickSeries.tsx # 预测K线图系列组件
│   │   │   └── ChartComposite.tsx # 图表组合组件 - 整合所有子组件
│   │   ├── ChartInfoBar.tsx       # 图表信息栏组件
│   │   ├── ChartInfoPanel.tsx     # 图表价格信息面板组件
│   │   └── PredictionLineChartLayer.tsx # 预测折线图层组件
│   ├── utils/
│   │   └── chartUtils.ts          # 图表相关工具函数
│   └── types/
│       └── chartTypes.ts          # 图表相关类型定义
```

## 组件说明

### 1. ChartCore

负责创建和管理图表实例，设置基本配置。提供了一个容器，可以将子组件注入其中。

```tsx
<ChartCore onChartReady={handleChartReady}>
  {/* 子组件将获得chart实例和isChartReady状态 */}
  <ChildComponent />
</ChartCore>
```

### 2. MainCandlestickSeries

管理主K线数据的加载、显示和更新。需要传入图表实例。

```tsx
<MainCandlestickSeries 
  chartApi={chartRef.current}
  isChartReady={isChartReady}
  candleData={candleData}
  onLoadMoreHistory={handleLoadMoreCandleHistory}
  onSeriesReady={handleMainSeriesReady}
/>
```

### 3. PredictionCandlestickSeries

管理预测K线数据的加载、显示、更新和闪烁效果。需要传入图表实例。

```tsx
<PredictionCandlestickSeries 
  chartApi={chartRef.current}
  isChartReady={isChartReady}
  predictionData={predictionData}
  showPrediction={showPrediction}
  onLoadMoreHistory={handleLoadMorePredictionHistory}
/>
```

### 4. ChartComposite

图表组合组件，将所有拆分的子组件组合在一起，提供与原Chart.tsx相同的API。

```tsx
<ChartComposite 
  candleData={candleData}
  predictionData={predictionData}
  isLoading={isLoading}
  onLoadMoreHistory={handleLoadMorePredictionHistory}
  onLoadMoreCandleHistory={handleLoadMoreCandleHistory}
  onChartReady={handleChartReady}
  showPrediction={showPrediction}
  showPredictionLine={showPredictionLine}
  chartType={chartType}
  symbol="BTC/USDT"
  timeframe="30M"
  onChartTypeChange={handleChartTypeChange}
/>
```

### 5. 工具函数 (chartUtils.ts)

提取出了通用的工具函数，如数据格式化、时间转换、图表配置等，减少代码重复和提高可维护性。

```ts
// 示例：使用工具函数获取图表配置
import { getDefaultChartOptions } from '../../utils/chartUtils';

const options = getDefaultChartOptions(containerWidth);
const chart = createChart(container, options);
```

## 类型定义 (chartTypes.ts)

集中定义了图表相关的类型，使代码更加类型安全和可维护。

```ts
// 示例：使用定义的类型
import { KLineData, PredictionData } from '../../types/chartTypes';

const [candleData, setCandleData] = useState<KLineData[]>([]);
const [predictionData, setPredictionData] = useState<PredictionData[]>([]);
```

## 使用示例

详细的使用示例可以查看 `ChartPage.tsx`，它展示了如何集成这些组件，以及管理数据和状态。

## 优势

1. **职责分离**：每个组件只负责一项具体功能，代码更清晰
2. **可维护性**：小组件更容易理解和维护
3. **可重用性**：组件可以在不同场景下重用
4. **可测试性**：独立组件更容易进行单元测试
5. **性能优化**：组件化架构有助于实现更精细的性能优化

## 未来改进

1. 增加单元测试覆盖
2. 实现图表主题切换功能
3. 添加更多技术指标组件
4. 优化移动设备体验 