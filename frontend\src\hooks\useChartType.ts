/**
 * 图表类型管理Hook
 * 
 * 负责管理图表类型(K线/折线)的状态切换
 */

import { useState, useCallback, useEffect } from 'react';

// 图表类型定义
export type ChartType = 'candlestick' | 'line';

interface ChartTypeConfig {
  chartType: ChartType;
  showPrediction: boolean;
  showPredictionLine: boolean;
}

/**
 * 图表类型管理Hook
 * 负责处理图表类型的存储和切换
 * @returns 图表类型状态和切换方法
 */
export const useChartType = () => {
  // 从localStorage获取保存的图表类型，如果没有则使用默认值
  const getSavedChartType = (): ChartType => {
    const savedType = localStorage.getItem('btc_chart_type');
    return (savedType === 'line' || savedType === 'candlestick') ? 
      savedType as ChartType : 'candlestick';
  };
  
  // 初始化图表类型状态
  const initialType = getSavedChartType();
  const [chartConfig, setChartConfig] = useState<ChartTypeConfig>({
    chartType: initialType,
    showPrediction: initialType === 'candlestick',
    showPredictionLine: true
  });
  
  // 处理图表类型变更
  const handleChartTypeChange = useCallback((newType: ChartType) => {
    // 如果当前类型和新类型相同，不做任何操作
    if (newType === chartConfig.chartType) {
      return;
    }
    
    console.log(`切换图表类型: ${chartConfig.chartType} -> ${newType}`);
    
    // 保存到本地存储
    localStorage.setItem('btc_chart_type', newType);
    
    // 更新状态
    setChartConfig({
      chartType: newType,
      showPrediction: newType === 'candlestick',
      showPredictionLine: true
    });
  }, [chartConfig.chartType]);

  return {
    chartType: chartConfig.chartType,
    showPrediction: chartConfig.showPrediction,
    showPredictionLine: chartConfig.showPredictionLine,
    handleChartTypeChange
  };
}; 