import React, { useState, useEffect } from 'react';
import inviteApi from '../api/invite';
import { Button } from './ui/button';
import { Loader2 } from 'lucide-react';
import { toast } from './ui/use-toast';

// 邀请码类型定义
interface InviteCode {
  _id: string;
  code: string;
  createdBy: string;
  usedBy: {
    _id: string;
    email: string;
    username: string;
  } | null;
  usedAt: string | null;
  createdAt: string;
}

// 组件属性类型
interface InviteCodesListProps {
  className?: string;
}

const InviteCodesList: React.FC<InviteCodesListProps> = ({ className = '' }) => {
  const [inviteCodes, setInviteCodes] = useState<InviteCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 格式化时间为本地时间
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 复制邀请码到剪贴板
  const copyToClipboard = (code: string) => {
    navigator.clipboard.writeText(code).then(
      () => {
        toast({
          title: '复制成功',
          description: '邀请码已复制到剪贴板',
          variant: 'default'
        });
      },
      (err) => {
        console.error('复制失败:', err);
        toast({
          title: '复制失败',
          description: '请手动复制邀请码',
          variant: 'destructive'
        });
      }
    );
  };

  // 获取邀请码列表
  useEffect(() => {
    const fetchInviteCodes = async () => {
      try {
        setLoading(true);
        const data = await inviteApi.getMyInviteCodes();
        setInviteCodes(data);
        setError(null);
      } catch (err) {
        setError('获取邀请码失败，请稍后重试');
        console.error('获取邀请码失败:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchInviteCodes();
  }, []);

  // 显示加载中状态
  if (loading) {
    return (
      <div className={`flex justify-center items-center py-12 ${className}`}>
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">正在加载邀请码...</span>
      </div>
    );
  }

  // 显示错误信息
  if (error) {
    return (
      <div className={`bg-error/10 border border-error/20 text-error px-4 py-3 rounded-md text-sm ${className}`}>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 邀请码管理卡片 */}
      <div className="border border-border rounded-lg p-5 bg-card">
        <h3 className="text-lg font-semibold mb-4">邀请码管理</h3>
        
        {/* 如果没有邀请码 */}
        {inviteCodes.length === 0 ? (
          <div className="p-4 rounded-lg text-center border border-border">
            <p className="text-content-secondary text-sm">你目前没有可用的邀请码</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-muted/50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-content-secondary whitespace-nowrap">
                    邀请码
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-content-secondary whitespace-nowrap">
                    使用状态
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-content-secondary whitespace-nowrap">
                    使用时间
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-content-secondary whitespace-nowrap">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-border">
                {inviteCodes.map((inviteCode) => (
                  <tr key={inviteCode._id}>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      <div className="font-mono">{inviteCode.code}</div>
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      {inviteCode.usedBy ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success/20 text-success">
                          已使用
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/20 text-primary">
                          可用
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap text-content-secondary">
                      {formatDate(inviteCode.usedAt)}
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      {!inviteCode.usedBy ? (
                        <Button
                          onClick={() => copyToClipboard(inviteCode.code)}
                          variant="ghost"
                          size="sm"
                          className="text-primary hover:text-content-primary bg-white/5"
                        >
                          复制
                        </Button>
                      ) : (
                        <span className="text-content-muted text-sm">已使用</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default InviteCodesList; 