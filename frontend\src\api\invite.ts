import api from './config'; // 使用统一的axios实例

/**
 * 获取当前用户的邀请码列表
 */
export const getMyInviteCodes = async () => {
  try {
    const response = await api.get('/invites/mine');
    return response.data.data;
  } catch (error) {
    console.error('获取邀请码列表失败', error);
    throw error;
  }
};

/**
 * 获取邀请码详情
 */
export const getInviteCodeDetails = async (code: string) => {
  try {
    const response = await api.get(`/invites/${code}`);
    return response.data.data;
  } catch (error) {
    console.error(`获取邀请码 ${code} 详情失败`, error);
    throw error;
  }
};

export default {
  getMyInviteCodes,
  getInviteCodeDetails,
}; 