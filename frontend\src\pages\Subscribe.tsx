import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Check, Zap, Clock } from 'lucide-react';
import { cn } from '../lib/utils';
import useUserStore from '../store/useUserStore';
import { createSubscription } from '../api/subscription';
import { toast } from '../components/ui/use-toast';
import { getPublicSettings } from '../api/public';

// 默认订阅计划数据（作为备用）
const defaultSubscriptionPlans = [
  {
    id: 'monthly',
    name: '月度订阅',
    price: '$39',
    duration: '30天',
    billingCycle: '每月',
    features: [
      '完整访问所有图表分析',
      '实时市场数据'
    ],
    popular: false,
    color: 'primary'
  },
  {
    id: 'quarterly',
    name: '季度订阅',
    price: '$87',
    duration: '90天',
    billingCycle: '每季度',
    features: [
      '完整访问所有图表分析',
      '实时市场数据',
      '每季度节省 $30'
    ],
    popular: true,
    color: 'success'
  },
  {
    id: 'yearly',
    name: '年度订阅',
    price: '$299',
    duration: '365天',
    billingCycle: '每年',
    features: [
      '完整访问所有图表分析',
      '实时市场数据',
      '每年节省 $169'
    ],
    popular: false,
    color: 'primary'
  }
];

const Subscribe: React.FC = () => {
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [subscriptionPlans, setSubscriptionPlans] = useState(defaultSubscriptionPlans);
  const { user } = useUserStore();

  // 从后端获取订阅价格（静默更新，不显示加载状态）
  useEffect(() => {
    const fetchPrices = async () => {
      try {
        const response = await getPublicSettings();

        if (response.settings.subscriptionPrices) {
          const prices = response.settings.subscriptionPrices;

          // 更新订阅计划价格
          const updatedPlans = defaultSubscriptionPlans.map(plan => {
            if (plan.id === 'monthly' && prices.monthly) {
              return { ...plan, price: `$${prices.monthly}` };
            } else if (plan.id === 'quarterly' && prices.quarterly) {
              return { ...plan, price: `$${prices.quarterly}` };
            } else if (plan.id === 'yearly' && prices.yearly) {
              return { ...plan, price: `$${prices.yearly}` };
            }
            return plan;
          });

          setSubscriptionPlans(updatedPlans);
        }
      } catch (error) {
        console.error('获取订阅价格失败', error);
        // 静默失败，继续使用默认价格
      }
    };

    fetchPrices();
  }, []);

  // 处理订阅
  const handleSubscribe = async (planId: string) => {
    try {
      setIsLoading(planId);

      // 创建订阅
      const response = await createSubscription(planId);

      // 跳转到支付页面
      window.location.href = response.payment.invoiceUrl;

    } catch (error) {
      console.error('订阅创建失败', error);
      toast({
        title: '订阅创建失败',
        description: '请稍后重试或联系客服',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(null);
    }
  };

  return (
    <div className="min-h-screen container mx-auto py-12 px-4">
      <div className="text-center mb-12">
        <h1 className="text-3xl text-white font-bold mb-4">选择最适合您的订阅计划</h1>
        <p className="text-content-secondary max-w-2xl mx-auto">
          订阅解锁AI预测分析服务，获取AI驱动的实时市场行情预测，提升您的投资决策能力。
        </p>

        {user?.role === 'trial' && (
          <div className="mt-4 p-3 bg-warning/10 text-warning border border-warning/20 rounded-md inline-block">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span>您的试用期将于 {new Date(user.trialEndsAt).toLocaleDateString()} 结束</span>
            </div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        {subscriptionPlans.map((plan) => (
            <Card
              key={plan.id}
              className={cn(
                "flex flex-col h-full transition-all",
                plan.popular ? "border-success shadow-md scale-105" : "border-border"
              )}
            >
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-xl">{plan.name}</CardTitle>
                    <CardDescription>{plan.billingCycle}</CardDescription>
                  </div>
                  {plan.popular && (
                    <span className="px-2.5 py-0.5 rounded-full text-xs bg-success/10 text-success font-medium">
                      最受欢迎
                    </span>
                  )}
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <div className="mb-6">
                  <span className="text-3xl font-bold">{plan.price}</span>
                  <span className="text-content-secondary ml-1">/ {plan.duration}</span>
                </div>

                <ul className="space-y-2 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className={cn("h-5 w-5 mr-2 flex-shrink-0",
                        plan.popular ? "text-success" : "text-primary")} />
                      <span className="text-content-secondary">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button
                  className={cn("w-full",
                    plan.popular
                      ? "bg-success hover:bg-success/90 text-white"
                      : "bg-primary hover:bg-primary/90"
                  )}
                  onClick={() => handleSubscribe(plan.id)}
                  disabled={!!isLoading}
                >
                  {isLoading === plan.id ? '处理中...' : '立即订阅'}
                  {plan.popular && <Zap className="ml-2 h-4 w-4" />}
                </Button>
              </CardFooter>
            </Card>
          ))
        }
      </div>

      <div className="mt-12 text-center text-content-secondary text-sm max-w-2xl mx-auto">
        <p>
          通过订阅，您同意我们的服务条款和隐私政策。所有支付通过NOWPayments加密货币支付处理，您的财务信息是安全的。
          如有任何问题，请联系我们。
        </p>
      </div>
    </div>
  );
};

export default Subscribe;