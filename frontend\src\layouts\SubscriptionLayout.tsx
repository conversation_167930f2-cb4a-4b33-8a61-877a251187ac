import React from 'react';
import { Outlet } from 'react-router-dom';
import { Logo } from '../components/Logo';

const SubscriptionLayout: React.FC = () => {
  return (
    <div className="min-h-screen bg-background text-content-primary flex flex-col">
      {/* 顶部导航 */}
      <header className="border-b border-border py-4">
        <div className="container mx-auto px-4 flex justify-center">
          <Logo />
        </div>
      </header>

      {/* 主内容区 */}
      <main className="flex-1 container mx-auto px-4 py-8">
        <Outlet />
      </main>

      {/* 页脚 */}
      <footer className="border-t border-border py-6">
        <div className="container mx-auto px-4 text-center text-content-secondary text-sm">
          &copy; {new Date().getFullYear()} 您的公司名称. 保留所有权利
        </div>
      </footer>
    </div>
  );
};

export default SubscriptionLayout; 