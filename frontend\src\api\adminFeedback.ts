import axios from 'axios';
import { getAdminAuthHeader } from './admin';

// 创建带有基础URL的axios实例
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 获取反馈详情（管理员专用）
export const getAdminFeedbackDetail = async (id: string) => {
  try {
    const headers = getAdminAuthHeader();
    const response = await api.get(`/admin/feedback/${id}`, { headers });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取反馈详情失败');
    }
    throw error;
  }
};

// 获取所有反馈列表
export const getAllFeedbacks = async (
  page = 1,
  limit = 10,
  status?: string,
  search?: string
) => {
  try {
    const headers = getAdminAuthHeader();
    let url = `/admin/feedback?page=${page}&limit=${limit}`;

    if (status) {
      url += `&status=${status}`;
    }

    if (search) {
      url += `&search=${encodeURIComponent(search)}`;
    }

    const response = await api.get(url, { headers });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取反馈列表失败');
    }
    throw error;
  }
};

// 回复反馈
export const replyFeedback = async (
  id: string,
  data: { reply: string; status?: string }
) => {
  try {
    const headers = getAdminAuthHeader();
    const response = await api.post(`/admin/feedback/${id}/reply`, data, { headers });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '回复反馈失败');
    }
    throw error;
  }
};

// 更新反馈状态
export const updateFeedbackStatus = async (
  id: string,
  status: 'pending' | 'processing' | 'replied'
) => {
  try {
    const headers = getAdminAuthHeader();
    const response = await api.patch(
      `/admin/feedback/${id}/status`,
      { status },
      { headers }
    );
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '更新反馈状态失败');
    }
    throw error;
  }
};

export default {
  getAdminFeedbackDetail,
  getAllFeedbacks,
  replyFeedback,
  updateFeedbackStatus
};
