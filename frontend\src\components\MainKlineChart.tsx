import React, { useCallback, useRef, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ChartComposite from './chart/ChartComposite';
import { IChartApi, ISeriesApi } from 'lightweight-charts';
import { useKLineData } from '../hooks/useKLineData';
import { usePredictionData } from '../hooks/usePredictionData';
import { useChartType } from '../hooks/useChartType';

// 组件属性接口定义
interface MainKlineChartProps {
  // 可以添加一些可选的属性来增强组件的可配置性
  refreshInterval?: number; // K线数据刷新间隔
  predictionRefreshInterval?: number; // 预测数据刷新间隔
  showLoadingIndicator?: boolean; // 是否显示加载指示器
  symbol?: string; // 交易对名称
  timeframe?: string; // 图表周期
}

/**
 * 主K线图表组件
 * 重构后使用自定义Hook分离职责，完全依赖图表管理器
 */
const MainKlineChart: React.FC<MainKlineChartProps> = ({
  refreshInterval = 10000, // 默认10秒刷新一次K线
  predictionRefreshInterval = 60000, // 默认60秒刷新一次预测
  showLoadingIndicator = true,
  symbol = 'BTC/USDT',
  timeframe = '30M'
}) => {
  const { t } = useTranslation(); // 国际化翻译钩子
  
  // 使用自定义Hook管理K线数据
  const { 
    candleData, 
    isLoading, 
    isLoadingMoreCandles, 
    error, 
    loadMoreHistory: loadMoreHistoryCandleData 
  } = useKLineData(refreshInterval);
  
  // 使用自定义Hook管理预测数据
  const { 
    predictionData, 
    isLoadingMore: isLoadingMorePredictions, 
    loadMoreHistory: loadMoreHistoryData 
  } = usePredictionData(predictionRefreshInterval);
  
  // 使用自定义Hook管理图表类型
  const { 
    chartType, 
    showPrediction, 
    showPredictionLine, 
    handleChartTypeChange 
  } = useChartType();
  
  // 图表引用 - 不再需要LineChartService
  const chartApiRef = useRef<IChartApi | null>(null);
  const mainSeriesRef = useRef<ISeriesApi<"Candlestick"> | null>(null);
  
  // 状态标志
  const [isChartReady, setIsChartReady] = useState(false);
  const [hasData, setHasData] = useState(false);

  // 监控数据状态变化
  useEffect(() => {
    setHasData(candleData.length > 0);
  }, [candleData]);

  // 图表就绪处理函数
  const handleChartReady = useCallback((chart: IChartApi, mainSeries: ISeriesApi<"Candlestick">) => {
    console.log('图表实例就绪');
    
    // 保存图表实例和主K线系列引用
    chartApiRef.current = chart;
    mainSeriesRef.current = mainSeries;
    setIsChartReady(true);
    
    // 不再需要初始化LineChartService，由图表管理器处理
  }, []);

  // 渲染图表组件
  return (
    <div>
      <div className="mb-4 flex justify-between items-center">
        <div className="flex space-x-2">
          {isLoading && showLoadingIndicator && (
            <span className="text-warning animate-pulse">
              {t('loading')}...
            </span>
          )}
          {(isLoadingMorePredictions || isLoadingMoreCandles) && showLoadingIndicator && (
            <span className="text-primary animate-pulse">
              {t('loading')} {t('chart.moreHistory')}...
            </span>
          )}
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-error text-content-primary p-4 mb-4 rounded-lg">
          {error}
        </div>
      )}

      {/* 图表组件 */}
      <ChartComposite 
        candleData={candleData} 
        predictionData={predictionData} 
        isLoading={isLoading}
        onLoadMoreHistory={loadMoreHistoryData}
        onLoadMoreCandleHistory={loadMoreHistoryCandleData}
        onChartReady={handleChartReady}
        showPrediction={showPrediction}
        showPredictionLine={showPredictionLine}
        chartType={chartType}
        symbol={symbol}
        timeframe={timeframe}
        onChartTypeChange={handleChartTypeChange}
      />
    </div>
  );
};

export default MainKlineChart; 