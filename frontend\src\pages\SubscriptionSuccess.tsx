import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { getSubscriptionInfo } from '../api/subscription';
import { Button } from '../components/ui/button';
import { CheckCircle, AlertCircle, Loader2, ArrowRight } from 'lucide-react';

const SubscriptionSuccess: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subscriptionInfo, setSubscriptionInfo] = useState<any>(null);
  const navigate = useNavigate();
  const location = useLocation();
  
  // 解析URL参数获取支付ID
  const paymentId = new URLSearchParams(location.search).get('NP_id');
  
  // 提取订阅计划类型
  const getPlanName = (type: string | null | undefined) => {
    switch (type) {
      case 'monthly':
        return '月度订阅';
      case 'quarterly':
        return '季度订阅';
      case 'yearly':
        return '年度订阅';
      default:
        return '订阅';
    }
  };
  
  // 格式化日期
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return '无';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };
  
  // 获取最新的订阅信息
  useEffect(() => {
    const fetchSubscriptionInfo = async () => {
      try {
        setLoading(true);

        // 获取最新订阅信息
        const response = await getSubscriptionInfo();
        setSubscriptionInfo(response.subscription);
        setError(null);
      } catch (err) {
        console.error('获取订阅信息失败:', err);
        setError('无法获取订阅信息，请稍后在用户中心查看');
      } finally {
        setLoading(false);
      }
    };

    fetchSubscriptionInfo();
  }, []);
  
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[50vh] p-6">
        <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
        <h2 className="text-xl font-semibold mb-2">正在处理您的订阅...</h2>
        <p className="text-content-secondary text-center max-w-md">
          我们正在确认您的付款，请稍等片刻
        </p>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[50vh] p-6">
        <div className="bg-error/10 text-error p-4 rounded-lg mb-6 flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
          <p>{error}</p>
        </div>
        <Button onClick={() => navigate('/dashboard')}>
          返回仪表盘
        </Button>
      </div>
    );
  }
  
  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh] p-6">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <CheckCircle className="h-16 w-16 text-success mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-2">订阅成功!</h1>
          <p className="text-content-secondary">
            感谢您的订阅，您现在可以访问所有高级功能
          </p>
        </div>
        
        {subscriptionInfo && (
          <div className="bg-card border rounded-lg p-6 mb-6">
            <h2 className="font-semibold text-lg mb-4">订阅详情</h2>
            <dl className="grid gap-3">
              <div className="grid grid-cols-3 gap-1">
                <dt className="text-content-secondary">订阅类型:</dt>
                <dd className="col-span-2 font-medium">{getPlanName(subscriptionInfo.type)}</dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="text-content-secondary">到期日期:</dt>
                <dd className="col-span-2 font-medium">{formatDate(subscriptionInfo.endDate)}</dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="text-content-secondary">订阅状态:</dt>
                <dd className="col-span-2 font-medium">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success/20 text-success">
                    已激活
                  </span>
                </dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="text-content-secondary">支付ID:</dt>
                <dd className="col-span-2 font-medium text-sm truncate">
                  {paymentId || '无'}
                </dd>
              </div>
            </dl>
          </div>
        )}
        
        <div className="flex flex-col gap-3">
          <Button 
            onClick={() => navigate('/dashboard')}
            className="w-full"
          >
            前往仪表盘 <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionSuccess; 