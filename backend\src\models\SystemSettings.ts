import mongoose, { Document, Schema, Model } from 'mongoose';

/**
 * 系统设置文档接口
 */
export interface SystemSettingsDocument extends Document {
  // 基础网站信息配置
  siteInfo: {
    siteName: string;               // 网站名称
    siteDescription: string;        // 网站描述
    siteKeywords: string;           // 网站关键词
    logoUrl: string;                // Logo URL
    faviconUrl: string;             // Favicon URL
    copyright: string;              // 版权信息
    socialLinks: {                  // 社交媒体链接
      twitter?: string;
      telegram?: string;
      facebook?: string;
      instagram?: string;
      github?: string;
    };
    defaultLanguage: 'zh' | 'en';   // 默认语言设置
  };

  // 用户设置
  userSettings: {
    enableRegistration: boolean;    // 是否开启注册
    inviteCodeRequired: boolean;    // 是否必须使用邀请码注册
    defaultRole: 'trial' | 'normal'; // 默认注册角色
    passwordMinLength: number;      // 密码最小长度
    passwordRequireSpecialChar: boolean; // 密码是否需要特殊字符
    allowedEmailDomains: string[];  // 允许的邮箱域名后缀，空数组表示允许所有
    trialDays: number;              // 默认试用天数
    inviteRewardDays: number;       // 邀请奖励天数
  };

  // 订阅价格设置（美元）- 保留原有结构
  subscriptionPrices: {
    monthly: number;                // 月度订阅价格
    quarterly: number;              // 季度订阅价格
    yearly: number;                 // 年度订阅价格
  };

  // 订阅支付设置
  paymentSettings: {
    provider: string;               // 支付提供商，如 'nowpayments'
    apiKey: string;                 // 支付API密钥
    apiSecretKey: string;           // 支付API密钥（敏感信息）
    apiBaseUrl: string;             // 支付API基础URL
    payCurrency: string;            // 支付币种，如 'usdttrc20'
    callbackUrl: string;            // 支付回调URL
    successUrl: string;             // 支付成功跳转URL
    cancelUrl: string;              // 支付取消跳转URL
    failureLimit: number;           // 支付失败限制次数
    failureLockTime: number;        // 支付失败锁定时间（小时）
  };

  // 系统安全设置
  securitySettings: {
    siteDomain: string;             // 当前站点域名
    apiDomain: string;              // API域名
    binanceApiUrls: string[];       // 币安API基础地址列表
    binanceApiTimeout: number;      // 币安API请求超时时间（毫秒）
    binanceApiRetryTimes: number;   // 币安API请求重试次数
    loginFailLimit: number;         // 登录失败限制次数
    loginLockTime: number;          // 登录失败锁定时间（小时）
  };

  // 其他系统设置可在此扩展
  updatedAt: Date;                  // 最后更新时间
  updatedBy?: mongoose.Types.ObjectId; // 最后更新人
}

const systemSettingsSchema = new Schema({
  // 基础网站信息配置
  siteInfo: {
    siteName: {
      type: String,
      required: true,
      default: 'BTC 预测'
    },
    siteDescription: {
      type: String,
      required: true,
      default: '基于先进的机器学习算法，为您提供精准的 BTC 价格预测服务'
    },
    siteKeywords: {
      type: String,
      required: true,
      default: 'BTC,比特币,预测,价格预测,加密货币'
    },
    logoUrl: {
      type: String,
      required: true,
      default: '/logo.png'
    },
    faviconUrl: {
      type: String,
      required: true,
      default: '/favicon.ico'
    },
    copyright: {
      type: String,
      required: true,
      default: '© 2024 BTC预测系统. 保留所有权利.'
    },
    socialLinks: {
      twitter: {
        type: String,
        default: ''
      },
      telegram: {
        type: String,
        default: ''
      },
      facebook: {
        type: String,
        default: ''
      },
      instagram: {
        type: String,
        default: ''
      },
      github: {
        type: String,
        default: ''
      }
    },
    defaultLanguage: {
      type: String,
      enum: ['zh', 'en'],
      default: 'zh'
    }
  },

  // 用户设置
  userSettings: {
    enableRegistration: {
      type: Boolean,
      required: true,
      default: true
    },
    inviteCodeRequired: {
      type: Boolean,
      required: true,
      default: false
    },
    defaultRole: {
      type: String,
      enum: ['trial', 'normal'],
      required: true,
      default: 'trial'
    },
    passwordMinLength: {
      type: Number,
      required: true,
      default: 6
    },
    passwordRequireSpecialChar: {
      type: Boolean,
      required: true,
      default: false
    },
    allowedEmailDomains: {
      type: [String],
      default: []
    },
    trialDays: {
      type: Number,
      required: true,
      default: 7
    },
    inviteRewardDays: {
      type: Number,
      required: true,
      default: 30
    }
  },

  // 订阅价格设置 - 保留原有结构
  subscriptionPrices: {
    monthly: {
      type: Number,
      required: true,
      default: 30
    },
    quarterly: {
      type: Number,
      required: true,
      default: 75
    },
    yearly: {
      type: Number,
      required: true,
      default: 266
    }
  },

  // 订阅支付设置
  paymentSettings: {
    provider: {
      type: String,
      required: true,
      default: 'nowpayments'
    },
    apiKey: {
      type: String,
      default: ''
    },
    apiSecretKey: {
      type: String,
      default: ''
    },
    apiBaseUrl: {
      type: String,
      required: true,
      default: 'https://api-sandbox.nowpayments.io/v1'
    },
    payCurrency: {
      type: String,
      required: true,
      default: 'usdttrc20'
    },
    callbackUrl: {
      type: String,
      default: ''
    },
    successUrl: {
      type: String,
      default: ''
    },
    cancelUrl: {
      type: String,
      default: ''
    },
    failureLimit: {
      type: Number,
      required: true,
      default: 5
    },
    failureLockTime: {
      type: Number,
      required: true,
      default: 24
    }
  },

  // 系统安全设置
  securitySettings: {
    siteDomain: {
      type: String,
      default: ''
    },
    apiDomain: {
      type: String,
      default: ''
    },
    binanceApiUrls: {
      type: [String],
      default: [
        'https://api.binance.com',
        'https://api1.binance.com',
        'https://api2.binance.com',
        'https://api3.binance.com',
        'https://api-gcp.binance.com'
      ]
    },
    binanceApiTimeout: {
      type: Number,
      required: true,
      default: 30000
    },
    binanceApiRetryTimes: {
      type: Number,
      required: true,
      default: 3
    },
    loginFailLimit: {
      type: Number,
      required: true,
      default: 5
    },
    loginLockTime: {
      type: Number,
      required: true,
      default: 2
    }
  },

  // 元数据
  updatedAt: {
    type: Date,
    default: Date.now
  },

  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: false
  }
});

/**
 * 确保只有一条系统设置记录
 * 使用静态方法获取或创建系统设置
 */
systemSettingsSchema.static('getSettings', async function(): Promise<SystemSettingsDocument> {
  const settings = await this.findOne();
  if (settings) {
    return settings;
  }

  // 如果没有设置，创建默认设置
  return this.create({
    siteInfo: {
      siteName: 'BTC 预测',
      siteDescription: '基于先进的机器学习算法，为您提供精准的 BTC 价格预测服务',
      siteKeywords: 'BTC,比特币,预测,价格预测,加密货币',
      logoUrl: '/logo.png',
      faviconUrl: '/favicon.ico',
      copyright: '© 2024 BTC预测系统. 保留所有权利.',
      socialLinks: {
        twitter: '',
        telegram: '',
        facebook: '',
        instagram: '',
        github: ''
      },
      defaultLanguage: 'zh'
    },
    userSettings: {
      enableRegistration: true,
      inviteCodeRequired: false,
      defaultRole: 'trial',
      passwordMinLength: 6,
      passwordRequireSpecialChar: false,
      allowedEmailDomains: [],
      trialDays: 7,
      inviteRewardDays: 30
    },
    subscriptionPrices: {
      monthly: 30,
      quarterly: 75,
      yearly: 266
    },
    paymentSettings: {
      provider: 'nowpayments',
      apiKey: '',
      apiSecretKey: '',
      apiBaseUrl: 'https://api-sandbox.nowpayments.io/v1',
      payCurrency: 'usdttrc20',
      callbackUrl: '',
      successUrl: '',
      cancelUrl: '',
      failureLimit: 5,
      failureLockTime: 24
    },
    securitySettings: {
      siteDomain: '',
      apiDomain: '',
      binanceApiUrls: [
        'https://api.binance.com',
        'https://api1.binance.com',
        'https://api2.binance.com',
        'https://api3.binance.com',
        'https://api-gcp.binance.com'
      ],
      binanceApiTimeout: 30000,
      binanceApiRetryTimes: 3,
      loginFailLimit: 5,
      loginLockTime: 2
    },
    updatedAt: new Date()
  });
});

/**
 * 系统设置模型接口 - 包含静态方法
 */
export interface SystemSettingsModel extends Model<SystemSettingsDocument> {
  getSettings(): Promise<SystemSettingsDocument>;
}

// 创建模型
const SystemSettings = mongoose.model<SystemSettingsDocument, SystemSettingsModel>(
  'SystemSettings',
  systemSettingsSchema
);

export default SystemSettings;