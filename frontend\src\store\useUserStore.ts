import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import api from '../api/auth';
import tokenService from '../api/tokenService';
import { User, UserState } from '../types/user';

// 使用类型断言确保类型安全
const useUserStore = create(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isLoading: false,
      error: null,
      isAuthenticated: false,
      isInitializing: true,

      // 登录
      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await api.login(email, password);

          // 设置到内存中
          if (response.token) {
            tokenService.setToken(response.token);
          }

          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            isLoading: false
          });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : '登录失败，请稍后重试';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 登出
      logout: () => {
        // 清除内存中的token
        tokenService.setToken(null);

        set({
          user: null,
          token: null,
          isAuthenticated: false
        });
      },

      // 注册
      register: async (email: string, password: string, inviteCode?: string) => {
        try {
          set({ isLoading: true, error: null });
          await api.register(email, password, inviteCode);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : '注册失败，请稍后重试';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 验证邮箱
      verifyEmail: async (token: string) => {
        try {
          set({ isLoading: true, error: null });
          await api.verifyEmail(token);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : '邮箱验证失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 忘记密码
      forgotPassword: async (email: string) => {
        try {
          set({ isLoading: true, error: null });
          await api.forgotPassword(email);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : '发送重置密码邮件失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 重置密码
      resetPassword: async (token: string, newPassword: string) => {
        try {
          set({ isLoading: true, error: null });
          await api.resetPassword(token, newPassword);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : '重置密码失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 修改密码
      changePassword: async (oldPassword: string, newPassword: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await api.changePassword(oldPassword, newPassword);
          set({ isLoading: false });

          // 如果后端要求重新认证，自动登出用户
          if (response.requireReauth) {
            // 清除内存中的token
            tokenService.setToken(null);

            set({
              user: null,
              token: null,
              isAuthenticated: false
            });
          }

          return response;
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : '修改密码失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 获取用户信息
      fetchUserInfo: async () => {
        try {
          set({ isLoading: true, error: null });

          // 尝试从内存或localStorage加载token
          const token = tokenService.getToken() || (() => {
            const state = get() as UserState;
            if (state.token) {
              tokenService.setToken(state.token);
              return state.token;
            }
            return null;
          })();

          if (!token) {
            set({
              isLoading: false,
              isInitializing: false
            });
            return;
          }

          try {
            // 先尝试使用当前token获取用户信息
          const userData = await api.getMe();
          set({
            user: userData,
            isAuthenticated: true,
            isLoading: false,
            isInitializing: false
          });
        } catch (error) {
            // 如果获取用户信息失败，尝试刷新令牌
            console.log('令牌可能已过期，尝试刷新...');
            const newToken = await api.refreshAccessToken();

            if (newToken) {
              // 刷新令牌成功，再次尝试获取用户信息
              try {
                const userData = await api.getMe();
                set({
                  user: userData,
                  token: newToken,  // 更新store中的token
                  isAuthenticated: true,
                  isLoading: false,
                  isInitializing: false
                });
                return;
              } catch (innerError) {
                console.error('即使刷新了令牌，获取用户信息仍然失败:', innerError);
                // 如果再次失败，清空用户状态
                tokenService.setToken(null);
                set({
                  isLoading: false,
                  isAuthenticated: false,
                  user: null,
                  token: null,
                  isInitializing: false
                });
              }
            } else {
              // 刷新令牌失败，清空用户状态
              console.error('刷新令牌失败');
              tokenService.setToken(null);
              set({
                isLoading: false,
                isAuthenticated: false,
                user: null,
                token: null,
                isInitializing: false
              });
            }
          }
        } catch (error) {
          // 处理其他可能的错误
          console.error('获取用户信息过程中出现错误:', error);
          tokenService.setToken(null);
          set({
            isLoading: false,
            isAuthenticated: false,
            user: null,
            token: null,
            isInitializing: false
          });
        }
      },

      // 刷新令牌
      refreshToken: async () => {
        try {
          const newToken = await api.refreshAccessToken();

          if (newToken) {
            set({ token: newToken });
            return true;
          }

          return false;
        } catch (error) {
          console.error('刷新令牌失败:', error);
          return false;
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      }
    }),
    {
      name: 'user-storage', // localStorage 中的键名
      partialize: (state) => ({
        // 使用类型断言确保类型安全
        token: (state as UserState).token,
        user: (state as UserState).user
      }),
    }
  )
);

export default useUserStore;