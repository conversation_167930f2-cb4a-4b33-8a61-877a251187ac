import React, { useEffect, useState } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import AuthForm from '../components/AuthForm';
import useUserStore from '../store/useUserStore';

const ResetPassword: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { resetPassword, isLoading, error, clearError, isInitializing } = useUserStore();
  const [resetSuccess, setResetSuccess] = useState(false);
  const [token, setToken] = useState<string | null>(null);
  const [passwordMinLength, setPasswordMinLength] = useState(6);
  const [passwordRequireSpecialChar, setPasswordRequireSpecialChar] = useState(false);
  
  // 从URL获取token
  const tokenFromParams = searchParams.get('token');
  
  // 清除之前的错误
  useEffect(() => {
    clearError();
  }, [clearError]);

  // 加载用户设置
  useEffect(() => {
    const loadUserSettings = async () => {
      try {
        const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:4000/api'}/config/user-settings`);
        const data = await response.json();

        if (data.success && data.settings) {
          setPasswordMinLength(data.settings.passwordMinLength || 6);
          setPasswordRequireSpecialChar(data.settings.passwordRequireSpecialChar || false);
        }
      } catch (error) {
        console.error('获取用户设置失败:', error);
        // 使用默认值
        setPasswordMinLength(6);
        setPasswordRequireSpecialChar(false);
      }
    };

    loadUserSettings();
  }, []);
  
  // 如果没有token，显示错误
  if (!tokenFromParams) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background px-4">
        <div className="w-full max-w-md bg-white dark:bg-background-card p-8 rounded-lg shadow-card text-center">
          <div className="text-red-600 text-5xl mb-4">✗</div>
          <h2 className="text-2xl font-bold mb-2 text-gray-800 dark:text-content-primary">无效的链接</h2>
          <p className="text-content-muted dark:text-content-secondary mb-4">
            重置密码链接无效或已过期。请重新尝试忘记密码流程。
          </p>
          <Link 
            to="/forgot-password" 
            className="inline-block bg-primary hover:bg-primary-dark text-content-primary py-2 px-4 rounded-md transition-colors duration-200"
          >
            重新获取重置链接
          </Link>
        </div>
      </div>
    );
  }
  
  // 处理重置密码表单提交
  const handleSubmit = async (formData: Record<string, string>) => {
    try {
      // 验证密码
      if (formData.password !== formData.confirmPassword) {
        throw new Error('两次输入的密码不一致');
      }

      // 验证密码长度
      if (formData.password.length < passwordMinLength) {
        throw new Error(`密码长度不能少于${passwordMinLength}位`);
      }

      // 验证特殊字符
      if (passwordRequireSpecialChar && !/[!@#$%^&*(),.?":{}|<>]/.test(formData.password)) {
        throw new Error('密码必须包含至少一个特殊字符');
      }

      await resetPassword(tokenFromParams, formData.password);
      setResetSuccess(true);

      // 3秒后重定向到登录页
      setTimeout(() => {
        navigate('/login', {
          state: { message: '密码已重置，请使用新密码登录' }
        });
      }, 3000);
    } catch (error) {
      // 错误已在store中处理
      console.error('重置密码失败', error);
    }
  };
  
  // 重置密码表单字段配置
  const getPasswordPlaceholder = () => {
    let placeholder = `请输入新密码（至少${passwordMinLength}位`;
    if (passwordRequireSpecialChar) {
      placeholder += '，需包含特殊字符';
    }
    placeholder += '）';
    return placeholder;
  };

  const fields = [
    {
      name: 'password',
      label: '新密码',
      type: 'password',
      placeholder: getPasswordPlaceholder()
    },
    {
      name: 'confirmPassword',
      label: '确认新密码',
      type: 'password',
      placeholder: '请再次输入新密码'
    }
  ];
  
  // 底部登录链接
  const footerText = (
    <>
      记起密码了？{' '}
      <Link to="/login" className="text-blue-600 hover:underline">
        立即登录
      </Link>
    </>
  );
  
  // 如果正在初始化，显示加载状态
  if (isInitializing) {
    return <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>;
  }
  
  // 如果密码已重置，显示成功信息
  if (resetSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background px-4">
        <div className="w-full max-w-md bg-white dark:bg-background-card p-8 rounded-lg shadow-card text-center">
          <div className="text-green-600 text-5xl mb-4">✓</div>
          <h2 className="text-2xl font-bold mb-2 text-gray-800 dark:text-content-primary">密码重置成功</h2>
          <p className="text-content-muted dark:text-content-secondary mb-4">
            您的密码已成功重置。请使用新密码登录。
          </p>
          <Link 
            to="/login" 
            className="inline-block bg-primary hover:bg-primary-dark text-content-primary py-2 px-4 rounded-md transition-colors duration-200"
          >
            立即登录
          </Link>
          <p className="mt-4 text-sm text-content-muted dark:text-content-secondary">页面将在3秒后自动跳转...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background px-4">
      <div className="w-full max-w-md">
        <AuthForm
          title="重置密码"
          fields={fields}
          submitText="重置密码"
          onSubmit={handleSubmit}
          isLoading={isLoading}
          error={error}
          footerText={footerText}
        />
      </div>
    </div>
  );
};

export default ResetPassword; 