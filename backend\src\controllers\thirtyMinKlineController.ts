import { Request, Response } from 'express';
import thirtyMinKlineService from '../services/thirtyMinKlineService';

/**
 * 30分钟K线数据控制器类
 * 处理与30分钟K线数据相关的HTTP请求
 */
class ThirtyMinKlineController {
  /**
   * 获取最近的30分钟K线数据
   * 获取并返回指定交易对的最近30分钟K线数据
   * 
   * @param req HTTP请求对象
   * @param res HTTP响应对象
   */
  async getRecentKlines(req: Request, res: Response): Promise<void> {
    try {
      // 获取查询参数，设置默认值
      const symbol = req.query.symbol as string || 'BTCUSDT';
      
      // 确保limit是有效的数字，默认值300
      const limit = req.query.limit ? 
        Math.min(1000, Math.max(1, parseInt(req.query.limit as string))) : 300;
      
      const endTime = req.query.endTime ? parseInt(req.query.endTime as string) : undefined;

      // 获取K线数据
      const klines = await thirtyMinKlineService.getRecentKlines(symbol, limit, endTime);
      
      // 转换为前端所需格式
      const formattedKlines = klines.map(kline => ({
        time: parseInt(String(kline.openTime)) / 1000,  // 转换为秒级时间戳
        open: parseFloat(kline.open),                   // 转换为数字
        high: parseFloat(kline.high),
        low: parseFloat(kline.low),
        close: parseFloat(kline.close),
        volume: parseFloat(kline.volume)
      }));
      
      // 返回JSON格式响应
      res.json(formattedKlines);
    } catch (error) {
      console.error('获取30分钟K线数据时出错:', error);
      res.status(500).json({ error: '获取30分钟K线数据失败' });
    }
  }
}

// 导出控制器实例
export default new ThirtyMinKlineController(); 