import { Router } from 'express';
import predictionRoutes from './predictionRoutes';
import thirtyMinKlineRoutes from './thirtyMinKlineRoutes';
import solarTermPredictionRoutes from './solarTermPredictionRoutes';
import authRoutes from './auth';
import inviteRoutes from './inviteRoutes';
import adminRoutes from './adminRoutes';
import subscriptionRoutes from './subscriptionRoutes';
import publicRoutes from './publicRoutes';
import feedbackRoutes from './feedbackRoutes';
import notificationRoutes from './notificationRoutes';
import announcementController from '../controllers/announcementController';
import configController from '../controllers/configController';
import userSettingsController from '../controllers/userSettingsController';

// 创建主路由实例
const router = Router();

// 注册K线数据相关的API路由


// 注册预测数据相关的API路由
router.use('/predictions', predictionRoutes);

// 注册30分钟K线数据相关的API路由
router.use('/thirty-min-klines', thirtyMinKlineRoutes);

// 注册节气预测折线API路由
router.use('/solar-term-predictions', solarTermPredictionRoutes);

// 注册用户认证相关的API路由
router.use('/auth', authRoutes);

// 注册邀请码相关的API路由
router.use('/invites', inviteRoutes);

// 注册管理员相关的API路由
router.use('/admin', adminRoutes);

// 注册订阅相关的API路由
router.use('/subscription', subscriptionRoutes);

// 注册公共API路由
router.use('/public', publicRoutes);

// 注册用户反馈相关的API路由
router.use('/feedback', feedbackRoutes);

// 注册用户通知相关的API路由
router.use('/notifications', notificationRoutes);

// 公告接口 (面向普通用户)
router.get('/announcements', announcementController.getVisibleAnnouncements);

// 公共配置接口
router.get('/config', configController.getPublicConfig);
router.get('/config/user-settings', userSettingsController.getUserSettings);

// 健康检查路由
// GET /api/health
router.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 静态文件调试路由
// GET /api/debug/static-files
router.get('/debug/static-files', (req, res) => {
  const fs = require('fs');
  const path = require('path');

  const publicPath = path.join(process.cwd(), 'public');
  const uploadsPath = path.join(publicPath, 'uploads');

  let files = [];

  try {
    if (fs.existsSync(uploadsPath)) {
      files = fs.readdirSync(uploadsPath).map((file: string) => ({
        name: file,
        path: `/uploads/${file}`,
        fullPath: path.join(uploadsPath, file)
      }));
    }

    res.json({
      success: true,
      publicPath,
      uploadsPath,
      uploadsExists: fs.existsSync(uploadsPath),
      files,
      baseUrl: `${req.protocol}://${req.get('host')}`
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    res.status(500).json({
      success: false,
      error: errorMessage,
      publicPath,
      uploadsPath
    });
  }
});

// 导出主路由
export default router;