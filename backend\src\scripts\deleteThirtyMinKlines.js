const mongoose = require('mongoose');

// 直接定义MongoDB连接URI，不再依赖config模块
const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/btc-prediction';

// 定义与ThirtyMinKline模型相同的Schema
const ThirtyMinKlineSchema = new mongoose.Schema({
  symbol: { type: String, required: true, index: true },
  openTime: { type: Number, required: true, index: true },
  closeTime: { type: Number, required: true },
  open: { type: String, required: true },
  high: { type: String, required: true },
  low: { type: String, required: true },
  close: { type: String, required: true },
  volume: { type: String, required: true },
  quoteVolume: { type: String, required: true },
  trades: { type: Number, required: true }
}, {
  timestamps: true,
  versionKey: false
});

// 创建复合索引
ThirtyMinKlineSchema.index({ symbol: 1, openTime: 1 }, { unique: true });

// 创建模型
const ThirtyMinKlineModel = mongoose.model('ThirtyMinKline', ThirtyMinKlineSchema);

/**
 * 删除30分钟K线数据脚本
 * 删除所有历史30分钟K线数据
 * 
 * 使用方法:
 * node src/scripts/deleteThirtyMinKlines.js [symbol]
 * 
 * 参数:
 * symbol - 交易对符号，如果不提供则删除所有交易对的数据
 * 
 * 示例:
 * node src/scripts/deleteThirtyMinKlines.js BTCUSDT - 仅删除BTCUSDT的30分钟K线数据
 * node src/scripts/deleteThirtyMinKlines.js - 删除所有交易对的30分钟K线数据
 */
async function deleteThirtyMinKlines() {
  try {
    // 解析命令行参数
    const args = process.argv.slice(2);
    const symbol = args[0]; // 可选，如果提供则只删除特定交易对的数据
    
    // 连接数据库
    console.log('正在连接数据库...');
    console.log(`MongoDB URI: ${MONGO_URI}`);
    await mongoose.connect(MONGO_URI);
    console.log('数据库连接成功');

    // 构建查询条件
    const query = symbol ? { symbol } : {};

    // 统计删除前的记录数
    const totalCount = await ThirtyMinKlineModel.countDocuments(query);
    console.log(`当前数据库中符合条件的30分钟K线记录共有${totalCount}条`);

    if (totalCount === 0) {
      console.log('没有需要删除的数据');
      await mongoose.connection.close();
      console.log('数据库连接已关闭');
      process.exit(0);
      return;
    }

    // 删除所有符合条件的30分钟K线数据
    console.log('开始删除30分钟K线数据...');
    const result = await ThirtyMinKlineModel.deleteMany(query);
    
    console.log(`成功删除${result.deletedCount}条30分钟K线记录`);
    console.log(`删除操作${result.acknowledged ? '已确认' : '未确认'}`);
    
    if (symbol) {
      console.log(`交易对 ${symbol} 的所有30分钟K线数据已清除`);
    } else {
      console.log('所有交易对的30分钟K线数据已清除');
    }
  } catch (error) {
    console.error('删除K线数据时出错:', error);
  } finally {
    // 关闭数据库连接
    if (mongoose.connection.readyState !== 0) { // 0 = disconnected
      await mongoose.connection.close();
      console.log('数据库连接已关闭');
    }
    process.exit(0);
  }
}

// 执行脚本
deleteThirtyMinKlines(); 