import React, { useEffect, useState } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import AnnouncementEditor from '../../components/admin/AnnouncementEditor';
import useAdminStore from '../../store/useAdminStore';

interface Announcement {
  _id: string;
  title: string;
  content: string;
  isVisible: boolean;
  isPinned: boolean;
  createdBy: {
    _id: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

const AdminAnnouncements: React.FC = () => {
  const { getAnnouncements, createAnnouncement, updateAnnouncement, deleteAnnouncement } = useAdminStore();
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [currentAnnouncement, setCurrentAnnouncement] = useState<Partial<Announcement> | null>(null);
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);

  // 获取所有公告
  const fetchAnnouncements = async () => {
    try {
      setIsLoading(true);
      setError('');
      const response = await getAnnouncements();
      setAnnouncements(response.announcements);
      setIsLoading(false);
    } catch (err) {
      const message = err instanceof Error ? err.message : '获取公告列表失败';
      setError(message);
      setIsLoading(false);
    }
  };

  // 初次加载获取数据
  useEffect(() => {
    fetchAnnouncements();
  }, []);  // eslint-disable-line react-hooks/exhaustive-deps

  // 创建/编辑公告
  const handleSaveAnnouncement = async (data: any) => {
    try {
      if (data._id) {
        // 更新现有公告
        await updateAnnouncement(data._id, {
          title: data.title,
          content: data.content,
          isVisible: data.isVisible,
          isPinned: data.isPinned
        });
      } else {
        // 创建新公告
        await createAnnouncement({
          title: data.title,
          content: data.content,
          isVisible: data.isVisible,
          isPinned: data.isPinned
        });
      }
      
      // 重新获取公告列表并重置编辑状态
      await fetchAnnouncements();
      setIsEditing(false);
      setCurrentAnnouncement(null);
    } catch (err) {
      const message = err instanceof Error ? err.message : '保存公告失败';
      throw new Error(message);
    }
  };

  // 删除公告
  const handleDeleteAnnouncement = async (id: string) => {
    try {
      setIsLoading(true);
      await deleteAnnouncement(id);
      setConfirmDelete(null);
      await fetchAnnouncements();
    } catch (err) {
      const message = err instanceof Error ? err.message : '删除公告失败';
      setError(message);
      setIsLoading(false);
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // 显示公告编辑器
  const showEditor = (announcement?: Announcement) => {
    if (announcement) {
      setCurrentAnnouncement(announcement);
    } else {
      setCurrentAnnouncement({
        title: '',
        content: '',
        isVisible: true,
        isPinned: false
      });
    }
    setIsEditing(true);
  };

  // 取消编辑
  const cancelEdit = () => {
    setIsEditing(false);
    setCurrentAnnouncement(null);
  };

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <button
            onClick={() => showEditor()}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            创建公告
          </button>
        </div>

        {error && (
          <div className="bg-error/10 border-l-4 border-red-500 text-red-700 p-4 mb-6">
            <p>{error}</p>
          </div>
        )}

        {isEditing ? (
          <AnnouncementEditor
            initialData={currentAnnouncement as any}
            onSubmit={handleSaveAnnouncement}
            onCancel={cancelEdit}
            isLoading={isLoading}
          />
        ) : isLoading ? (
          <div className="flex justify-center my-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : announcements.length === 0 ? (
          <div className="bg-white dark:bg-background-card shadow-card rounded-lg p-6 text-center">
            <p className="text-content-muted dark:text-content-secondary">暂无公告</p>
            <button
              onClick={() => showEditor()}
              className="mt-4 px-4 py-2 bg-blue-500 text-content-primary rounded-md hover:bg-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              创建第一个公告
            </button>
          </div>
        ) : (
          <div className="space-y-6">
            {announcements.map((announcement) => (
              <div
                key={announcement._id}
                className="bg-white dark:bg-background-card shadow-card rounded-lg p-6"
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <div className="flex items-center">
                      <h3 className="text-xl font-semibold text-gray-800 dark:text-content-primary">
                        {announcement.title}
                      </h3>
                      <div className="ml-3 space-x-2">
                        {announcement.isPinned && (
                          <span className="px-2 py-1 text-xs font-semibold rounded-full bg-warning/10 text-yellow-800">
                            置顶
                          </span>
                        )}
                        {!announcement.isVisible && (
                          <span className="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                            隐藏
                          </span>
                        )}
                      </div>
                    </div>
                    <p className="text-sm text-content-muted dark:text-content-secondary mt-1">
                      由 {announcement.createdBy?.email || '未知'} 创建于{' '}
                      {formatDate(announcement.createdAt)}
                      {announcement.updatedAt !== announcement.createdAt && 
                        `，更新于 ${formatDate(announcement.updatedAt)}`}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => showEditor(announcement)}
                      className="text-blue-600 hover:text-blue-900 dark:text-primary dark:hover:text-blue-300"
                    >
                      编辑
                    </button>
                    <button
                      onClick={() => setConfirmDelete(announcement._id)}
                      className="text-red-600 hover:text-red-900 dark:text-error dark:hover:text-red-300"
                    >
                      删除
                    </button>
                  </div>
                </div>
                <div className="prose dark:prose-invert max-w-none mt-2">
                  <p className="text-gray-700 dark:text-content-secondary whitespace-pre-line">
                    {announcement.content}
                  </p>
                </div>

                {/* 删除确认对话框 */}
                {confirmDelete === announcement._id && (
                  <div className="mt-4 p-4 border border-red-200 rounded-md bg-red-50 dark:bg-red-900 dark:border-red-800">
                    <p className="text-red-700 dark:text-red-200 mb-3">确定要删除此公告吗？此操作不可撤销。</p>
                    <div className="flex justify-end space-x-3">
                      <button
                        onClick={() => setConfirmDelete(null)}
                        className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 dark:bg-background-input dark:text-content-secondary dark:hover:bg-gray-600"
                      >
                        取消
                      </button>
                      <button
                        onClick={() => handleDeleteAnnouncement(announcement._id)}
                        className="px-3 py-1 bg-error text-content-primary rounded-md hover:bg-red-700"
                      >
                        确认删除
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminAnnouncements; 