import { FIRST_SOLAR_TERMS } from './solarTermData';

/**
 * 获取北京时间（UTC+8）
 * @param date 输入日期，默认为当前时间
 * @returns 调整为北京时间的日期对象
 */
export const getBeijingDate = (date: Date = new Date()): Date => {
  // 创建副本避免修改原始对象
  const beijingDate = new Date(date.getTime());
  // 调整为北京时间（UTC+8）
  beijingDate.setUTCHours(beijingDate.getUTCHours() + 8);
  return beijingDate;
};

/**
 * 格式化日期为YYYY-MM-DD格式
 * @param year 年份
 * @param month 月份(1-12)
 * @param day 日期
 * @returns 格式化后的日期字符串
 */
export const formatDate = (year: number, month: number, day: number): string => {
  return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
};

/**
 * 检查是否是指定月份的第一个节气日
 * @param year 年份
 * @param month 月份(1-12)
 * @param day 日期
 * @returns 如果是该月第一个节气日则返回true，否则返回false
 */
export const isMonthFirstSolarTermDay = (year: number, month: number, day: number): boolean => {
  const yearData = FIRST_SOLAR_TERMS[year.toString()];
  if (!yearData) {
    console.warn(`未找到${year}年的节气数据，请更新节气表`);
    return false;
  }

  const firstSolarTermDay = yearData[month.toString()];
  if (!firstSolarTermDay) {
    console.warn(`未找到${year}年${month}月的第一个节气日`);
    return false;
  }

  return day === firstSolarTermDay;
};

/**
 * 获取指定月份的第一个节气日
 * @param year 年份
 * @param month 月份(1-12)
 * @returns 该月第一个节气的日期，如果不存在则返回null
 */
export const getMonthFirstSolarTerm = (year: number, month: number): { name: string, day: number } | null => {
  const yearData = FIRST_SOLAR_TERMS[year.toString()];
  if (!yearData) {
    console.warn(`未找到${year}年的节气数据，请更新节气表`);
    return null;
  }

  const firstSolarTermDay = yearData[month.toString()];
  if (!firstSolarTermDay) {
    console.warn(`未找到${year}年${month}月的第一个节气日`);
    return null;
  }

  // 简化处理，只返回日期和默认名称
  return {
    name: `${month}月节气`,
    day: firstSolarTermDay
  };
};

/**
 * 获取下个月的第一个节气日期
 * @param date 当前日期
 * @returns 下个月第一个节气的日期对象，不存在则返回null
 */
export const getNextMonthFirstSolarTerm = (date: Date): Date | null => {
  const beijingDate = getBeijingDate(date);
  const year = beijingDate.getFullYear();
  const month = beijingDate.getMonth() + 1; // JS月份从0开始
  
  // 计算下个月
  let nextMonth = month + 1;
  let nextYear = year;
  
  if (nextMonth > 12) {
    nextMonth = 1;
    nextYear++;
  }
  
  // 获取下个月第一个节气
  const nextMonthTerm = getMonthFirstSolarTerm(nextYear, nextMonth);
  if (!nextMonthTerm) return null;
  
  // 创建节气日期（月份-1是因为JS月份从0开始）
  const solarTermDate = new Date(nextYear, nextMonth - 1, nextMonthTerm.day);
  solarTermDate.setHours(0, 0, 0, 0);
  return solarTermDate;
}; 