import { Request, Response } from 'express';
import Announcement from '../models/Announcement';
import mongoose from 'mongoose';

/**
 * 获取所有公告
 * GET /api/admin/announcements
 */
export const getAnnouncements = async (req: Request, res: Response) => {
  try {
    const announcements = await Announcement.find()
      .sort({ isPinned: -1, createdAt: -1 })
      .populate('createdBy', 'email');

    res.json({
      success: true,
      announcements
    });
  } catch (error) {
    console.error('获取公告列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 创建公告
 * POST /api/admin/announcements
 */
export const createAnnouncement = async (req: Request, res: Response) => {
  try {
    const { title, content, isVisible, isPinned } = req.body;

    // 验证请求体
    if (!title || !content) {
      return res.status(400).json({
        success: false,
        message: '请提供标题和内容'
      });
    }

    // 创建新公告
    const announcement = await Announcement.create({
      title,
      content,
      isVisible: isVisible !== undefined ? isVisible : true,
      isPinned: isPinned !== undefined ? isPinned : false,
      createdBy: req.user!._id
    });

    // 返回新创建的公告
    res.status(201).json({
      success: true,
      announcement
    });
  } catch (error) {
    console.error('创建公告错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 更新公告
 * PATCH /api/admin/announcements/:id
 */
export const updateAnnouncement = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { title, content, isVisible, isPinned } = req.body;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: '无效的公告ID'
      });
    }

    // 构建更新对象
    const updateData: any = {};
    if (title !== undefined) updateData.title = title;
    if (content !== undefined) updateData.content = content;
    if (isVisible !== undefined) updateData.isVisible = isVisible;
    if (isPinned !== undefined) updateData.isPinned = isPinned;

    // 查找并更新公告
    const announcement = await Announcement.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).populate('createdBy', 'email');

    if (!announcement) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }

    res.json({
      success: true,
      announcement
    });
  } catch (error) {
    console.error('更新公告错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 删除公告
 * DELETE /api/admin/announcements/:id
 */
export const deleteAnnouncement = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: '无效的公告ID'
      });
    }

    // 查找并删除公告
    const announcement = await Announcement.findByIdAndDelete(id);

    if (!announcement) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }

    res.json({
      success: true,
      message: '公告已成功删除'
    });
  } catch (error) {
    console.error('删除公告错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

// 为用户获取可见公告的接口
export const getVisibleAnnouncements = async (req: Request, res: Response) => {
  try {
    const announcements = await Announcement.find({ isVisible: true })
      .sort({ isPinned: -1, createdAt: -1 })
      .select('-createdBy');

    res.json({
      success: true,
      announcements
    });
  } catch (error) {
    console.error('获取可见公告错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

export default {
  getAnnouncements,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement,
  getVisibleAnnouncements
}; 