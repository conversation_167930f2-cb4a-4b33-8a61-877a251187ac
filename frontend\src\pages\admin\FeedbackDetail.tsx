import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import AdminLayout from '../../components/admin/AdminLayout';
import { getAdminFeedbackDetail, replyFeedback, updateFeedbackStatus } from '../../api/adminFeedback';
import { Button, Textarea, Select, Card } from '../../components/admin/ui';
import { useToast } from '../../components/ui/use-toast';
import { ArrowLeft } from 'lucide-react';

interface Feedback {
  _id: string;
  userId: string;
  userEmail: string;
  title: string;
  content: string;
  status: 'pending' | 'processing' | 'replied';
  adminReply?: string;
  replyAt?: string;
  createdAt: string;
  updatedAt: string;
}

const AdminFeedbackDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [feedback, setFeedback] = useState<Feedback | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [reply, setReply] = useState('');
  const [status, setStatus] = useState<'pending' | 'processing' | 'replied'>('processing');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  // 获取反馈详情
  useEffect(() => {
    const fetchFeedbackDetail = async () => {
      if (!id) return;

      try {
        setIsLoading(true);
        const response = await getAdminFeedbackDetail(id);
        setFeedback(response.data);

        // 如果已有回复，预填充回复内容
        if (response.data.adminReply) {
          setReply(response.data.adminReply);
        }

        // 设置当前状态
        setStatus(response.data.status);
      } catch (error) {
        console.error('获取反馈详情失败:', error);
        toast({
          title: '获取反馈详情失败',
          description: error instanceof Error ? error.message : '请稍后重试',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchFeedbackDetail();
  }, [id, toast]);

  // 提交回复
  const handleSubmitReply = async () => {
    if (!id) return;

    if (!reply.trim()) {
      toast({
        title: '请输入回复内容',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await replyFeedback(id, { reply, status });

      toast({
        title: '回复成功',
        variant: 'default'
      });

      // 更新本地状态
      if (feedback) {
        setFeedback({
          ...feedback,
          adminReply: reply,
          status,
          replyAt: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('提交回复失败:', error);
      toast({
        title: '提交回复失败',
        description: error instanceof Error ? error.message : '请稍后重试',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 更新状态
  const handleUpdateStatus = async (newStatus: 'pending' | 'processing' | 'replied') => {
    if (!id) return;

    try {
      await updateFeedbackStatus(id, newStatus);

      // 更新本地状态
      setStatus(newStatus);
      if (feedback) {
        setFeedback({
          ...feedback,
          status: newStatus
        });
      }

      toast({
        title: '状态更新成功',
        variant: 'default'
      });
    } catch (error) {
      console.error('更新状态失败:', error);
      toast({
        title: '更新状态失败',
        description: error instanceof Error ? error.message : '请稍后重试',
        variant: 'destructive'
      });
    }
  };

  // 返回列表页
  const handleBack = () => {
    navigate('/admin/feedback');
  };

  // 格式化日期
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '暂无';

    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 渲染状态标签
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <span className="px-2 py-1 text-xs rounded-md bg-yellow-100 text-yellow-800">未处理</span>;
      case 'processing':
        return <span className="px-2 py-1 text-xs rounded-md bg-blue-100 text-blue-800">处理中</span>;
      case 'replied':
        return <span className="px-2 py-1 text-xs rounded-md bg-green-100 text-green-800">已回复</span>;
      default:
        return null;
    }
  };

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="mb-6 flex items-center">
          <Button variant="outline" onClick={handleBack} className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">反馈详情</h1>
            <p className="text-gray-600 mt-1">查看和回复用户反馈</p>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        ) : feedback ? (
          <Card>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <h3 className="text-sm font-medium text-gray-600 mb-1">用户邮箱</h3>
                <p className="text-gray-900">{feedback.userEmail}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-600 mb-1">提交时间</h3>
                <p className="text-gray-900">{formatDate(feedback.createdAt)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-600 mb-1">状态</h3>
                <div className="flex items-center">
                  {renderStatusBadge(feedback.status)}
                  <div className="ml-4">
                    <Select
                      value={status}
                      onChange={(e) => handleUpdateStatus(e.target.value as any)}
                      options={[
                        { value: 'pending', label: '未处理' },
                        { value: 'processing', label: '处理中' },
                        { value: 'replied', label: '已回复' }
                      ]}
                      className="w-32"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">{feedback.title}</h3>
              <div className="p-4 bg-gray-50 rounded-md whitespace-pre-wrap border border-gray-200">
                {feedback.content}
              </div>
            </div>

            {feedback.adminReply && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-600 mb-1">
                  管理员回复 ({formatDate(feedback.replyAt)})
                </h3>
                <div className="p-4 bg-blue-50 rounded-md whitespace-pre-wrap border border-blue-200">
                  {feedback.adminReply}
                </div>
              </div>
            )}

            <div className="mt-8">
              <h3 className="text-lg font-medium text-gray-900 mb-2">回复反馈</h3>
              <Textarea
                value={reply}
                onChange={(e) => setReply(e.target.value)}
                placeholder="请输入回复内容..."
                rows={5}
                className="mb-4"
              />
              <Button onClick={handleSubmitReply} disabled={isSubmitting}>
                {isSubmitting ? '提交中...' : '提交回复'}
              </Button>
            </div>
          </Card>
        ) : (
          <div className="text-center py-8 text-gray-500">
            未找到反馈信息
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminFeedbackDetail;
