const { MongoClient } = require('mongodb');

// 数据库连接URI
const uri = 'mongodb://localhost:27017/btc-prediction';

async function cleanupDatabase() {
  const client = new MongoClient(uri);
  
  try {
    // 连接到MongoDB
    await client.connect();
    console.log('MongoDB连接成功');
    
    // 获取数据库和集合
    const db = client.db('btc-prediction');
    const predictionsCollection = db.collection('predictions');
    
    // 获取所有索引
    const indexes = await predictionsCollection.indexes();
    console.log('当前所有索引:');
    for (const index of indexes) {
      console.log(`- ${index.name}: ${JSON.stringify(index.key)}`);
    }
    
    // 查找并删除有问题的索引
    for (const index of indexes) {
      if (index.name && (
        index.name.includes('sourceTimestamp') || 
        index.name.includes('targetTimestamp')
      )) {
        console.log(`正在删除索引: ${index.name}`);
        try {
          await predictionsCollection.dropIndex(index.name);
          console.log(`索引 ${index.name} 已删除`);
        } catch (err) {
          console.error(`删除索引 ${index.name} 失败:`, err.message);
        }
      }
    }
    
    // 清理具有null值的预测数据
    console.log('正在清理无效的预测数据...');
    const deleteResult = await predictionsCollection.deleteMany({
      $or: [
        { sourceKlineTime: null },
        { targetStartTime: null },
        { targetEndTime: null }
      ]
    });
    console.log(`已删除 ${deleteResult.deletedCount} 条无效预测数据`);
    
    // 检查是否已存在要创建的索引
    const updatedIndexes = await predictionsCollection.indexes();
    const hasSymbolTargetStartTimeIndex = updatedIndexes.some(
      idx => idx.name === 'symbol_targetStartTime_unique' || 
            (idx.key && idx.key.symbol === 1 && idx.key.targetStartTime === 1)
    );
    
    // 创建新的索引（如果不存在）
    if (!hasSymbolTargetStartTimeIndex) {
      console.log('正在创建新索引...');
      try {
        await predictionsCollection.createIndex(
          { symbol: 1, targetStartTime: 1 },
          { unique: true, name: 'symbol_targetStartTime_unique' }
        );
        console.log('新索引创建完成');
      } catch (err) {
        console.error('创建新索引失败:', err.message);
      }
    } else {
      console.log('symbol_targetStartTime索引已存在，无需创建');
    }
    
    // 验证索引更新
    const finalIndexes = await predictionsCollection.indexes();
    console.log('更新后的索引:');
    for (const index of finalIndexes) {
      console.log(`- ${index.name}: ${JSON.stringify(index.key)}`);
    }
    
    console.log('数据库清理完成');
  } catch (error) {
    console.error('数据库清理出错:', error);
  } finally {
    // 关闭连接
    await client.close();
    console.log('MongoDB连接已关闭');
  }
}

// 执行清理
cleanupDatabase().catch(console.error); 