import React, { useState, useEffect } from 'react';
import AdminLayout from '../../../components/admin/AdminLayout';
import useAdminStore from '../../../store/useAdminStore';
import { useToast } from '../../../components/ui/use-toast';
import { Button, Input, Label, Card } from '../../../components/admin/ui';

/**
 * 订阅支付设置页面
 * 管理支付相关配置
 */
const PaymentSettings: React.FC = () => {
  const { getSystemSettings, updateSystemSettings } = useAdminStore();
  const { toast } = useToast();

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // 初始化表单数据
  const [formData, setFormData] = useState({
    paymentSettings: {
      provider: 'nowpayments',
      apiKey: '',
      apiSecretKey: '',
      apiBaseUrl: 'https://api-sandbox.nowpayments.io/v1',
      payCurrency: 'usdttrc20',
      callbackUrl: '',
      successUrl: '',
      cancelUrl: '',
      failureLimit: 5,
      failureLockTime: 24
    },
    subscriptionPrices: {
      monthly: 30,
      quarterly: 75,
      yearly: 266
    }
  });

  // 获取系统设置
  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      const response = await getSystemSettings();

      setFormData({
        paymentSettings: response.settings.paymentSettings || formData.paymentSettings,
        subscriptionPrices: response.settings.subscriptionPrices || formData.subscriptionPrices
      });

      setIsLoading(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取系统设置失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };

  // 初次加载时获取设置
  useEffect(() => {
    fetchSettings();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理表单输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // 处理嵌套属性
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData({
        ...formData,
        [parent]: {
          ...formData[parent as keyof typeof formData],
          [child]: value
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // 处理数字输入变化
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // 处理嵌套属性
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData({
        ...formData,
        [parent]: {
          ...formData[parent as keyof typeof formData],
          [child]: value === '' ? '' : Number(value)
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value === '' ? '' : Number(value)
      });
    }
  };

  // 保存设置
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSaving(true);
      await updateSystemSettings({
        paymentSettings: formData.paymentSettings,
        subscriptionPrices: formData.subscriptionPrices
      });
      toast({
        title: '支付设置已成功更新',
        variant: 'default'
      });
      setIsSaving(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新支付设置失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsSaving(false);
    }
  };

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">订阅支付设置</h1>
          <p className="text-gray-600 mt-1">管理支付相关配置和订阅价格设置</p>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <Card>
            <form onSubmit={handleSubmit}>
            {/* 订阅套餐价格设置 */}
            <div className="mb-6">
              <h2 className="text-lg font-medium mb-4 border-b pb-2">订阅套餐价格设置</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* 月度订阅价格 */}
                <div className="col-span-1">
                  <Label htmlFor="subscriptionPrices.monthly">月度订阅价格（美元）</Label>
                  <Input
                    id="subscriptionPrices.monthly"
                    name="subscriptionPrices.monthly"
                    type="number"
                    min="1"
                    step="0.01"
                    value={formData.subscriptionPrices.monthly}
                    onChange={handleNumberChange}
                    placeholder="月度订阅价格"
                    disabled={isSaving}
                  />
                  <p className="text-xs text-gray-500 mt-1">30天订阅的价格</p>
                </div>

                {/* 季度订阅价格 */}
                <div className="col-span-1">
                  <Label htmlFor="subscriptionPrices.quarterly">季度订阅价格（美元）</Label>
                  <Input
                    id="subscriptionPrices.quarterly"
                    name="subscriptionPrices.quarterly"
                    type="number"
                    min="1"
                    step="0.01"
                    value={formData.subscriptionPrices.quarterly}
                    onChange={handleNumberChange}
                    placeholder="季度订阅价格"
                    disabled={isSaving}
                  />
                  <p className="text-xs text-gray-500 mt-1">90天订阅的价格</p>
                </div>

                {/* 年度订阅价格 */}
                <div className="col-span-1">
                  <Label htmlFor="subscriptionPrices.yearly">年度订阅价格（美元）</Label>
                  <Input
                    id="subscriptionPrices.yearly"
                    name="subscriptionPrices.yearly"
                    type="number"
                    min="1"
                    step="0.01"
                    value={formData.subscriptionPrices.yearly}
                    onChange={handleNumberChange}
                    placeholder="年度订阅价格"
                    disabled={isSaving}
                  />
                  <p className="text-xs text-gray-500 mt-1">365天订阅的价格</p>
                </div>
              </div>
            </div>

            {/* 支付接口设置 */}
            <h2 className="text-lg font-medium mb-4 border-b pb-2">支付接口设置</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 支付提供商 */}
              <div className="col-span-1">
                <Label htmlFor="paymentSettings.provider">支付提供商</Label>
                <Input
                  id="paymentSettings.provider"
                  name="paymentSettings.provider"
                  value={formData.paymentSettings.provider}
                  onChange={handleChange}
                  placeholder="支付提供商"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">当前仅支持 NOWPayments</p>
              </div>

              {/* API密钥 */}
              <div className="col-span-1">
                <Label htmlFor="paymentSettings.apiKey">API密钥</Label>
                <Input
                  id="paymentSettings.apiKey"
                  name="paymentSettings.apiKey"
                  value={formData.paymentSettings.apiKey}
                  onChange={handleChange}
                  placeholder="API密钥"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">从NOWPayments获取的API密钥</p>
              </div>

              {/* API密钥（敏感） */}
              <div className="col-span-1">
                <Label htmlFor="paymentSettings.apiSecretKey">API密钥（敏感）</Label>
                <Input
                  id="paymentSettings.apiSecretKey"
                  name="paymentSettings.apiSecretKey"
                  type="password"
                  value={formData.paymentSettings.apiSecretKey}
                  onChange={handleChange}
                  placeholder="API密钥（敏感）"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">用于签名验证的密钥，请妥善保管</p>
              </div>

              {/* API基础URL */}
              <div className="col-span-1">
                <Label htmlFor="paymentSettings.apiBaseUrl">API基础URL</Label>
                <Input
                  id="paymentSettings.apiBaseUrl"
                  name="paymentSettings.apiBaseUrl"
                  value={formData.paymentSettings.apiBaseUrl}
                  onChange={handleChange}
                  placeholder="API基础URL"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">沙盒环境: https://api-sandbox.nowpayments.io/v1</p>
              </div>

              {/* 支付币种 */}
              <div className="col-span-1">
                <Label htmlFor="paymentSettings.payCurrency">支付币种</Label>
                <Input
                  id="paymentSettings.payCurrency"
                  name="paymentSettings.payCurrency"
                  value={formData.paymentSettings.payCurrency}
                  onChange={handleChange}
                  placeholder="支付币种"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">例如: usdttrc20, btc, eth</p>
              </div>

              {/* 支付回调URL */}
              <div className="col-span-1">
                <Label htmlFor="paymentSettings.callbackUrl">支付回调URL</Label>
                <Input
                  id="paymentSettings.callbackUrl"
                  name="paymentSettings.callbackUrl"
                  value={formData.paymentSettings.callbackUrl}
                  onChange={handleChange}
                  placeholder="支付回调URL"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">例如: https://yourdomain.com/api/subscription/webhook</p>
              </div>

              {/* 支付成功URL */}
              <div className="col-span-1">
                <Label htmlFor="paymentSettings.successUrl">支付成功URL</Label>
                <Input
                  id="paymentSettings.successUrl"
                  name="paymentSettings.successUrl"
                  value={formData.paymentSettings.successUrl}
                  onChange={handleChange}
                  placeholder="支付成功URL"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">例如: https://yourdomain.com/subscription/success</p>
              </div>

              {/* 支付取消URL */}
              <div className="col-span-1">
                <Label htmlFor="paymentSettings.cancelUrl">支付取消URL</Label>
                <Input
                  id="paymentSettings.cancelUrl"
                  name="paymentSettings.cancelUrl"
                  value={formData.paymentSettings.cancelUrl}
                  onChange={handleChange}
                  placeholder="支付取消URL"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">例如: https://yourdomain.com/subscription/cancel</p>
              </div>

              {/* 支付失败限制次数 */}
              <div className="col-span-1">
                <Label htmlFor="paymentSettings.failureLimit">支付失败限制次数</Label>
                <Input
                  id="paymentSettings.failureLimit"
                  name="paymentSettings.failureLimit"
                  type="number"
                  min="1"
                  value={formData.paymentSettings.failureLimit}
                  onChange={handleNumberChange}
                  placeholder="支付失败限制次数"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">连续失败多少次后锁定</p>
              </div>

              {/* 支付失败锁定时间 */}
              <div className="col-span-1">
                <Label htmlFor="paymentSettings.failureLockTime">支付失败锁定时间（小时）</Label>
                <Input
                  id="paymentSettings.failureLockTime"
                  name="paymentSettings.failureLockTime"
                  type="number"
                  min="1"
                  value={formData.paymentSettings.failureLockTime}
                  onChange={handleNumberChange}
                  placeholder="支付失败锁定时间"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">锁定多少小时后可再次尝试</p>
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <Button
                type="submit"
                className="bg-primary text-white"
                disabled={isSaving}
              >
                {isSaving ? '保存中...' : '保存设置'}
              </Button>
            </div>
            </form>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
};

export default PaymentSettings;
