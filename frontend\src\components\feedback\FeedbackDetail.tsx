import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogFooter,
} from '../../components/ui/dialog';
import { Button } from '../../components/ui/button';

interface Feedback {
  _id: string;
  title: string;
  content: string;
  status: 'pending' | 'processing' | 'replied';
  adminReply?: string;
  replyAt?: string;
  createdAt: string;
}

interface FeedbackDetailProps {
  feedback: Feedback;
  onClose: () => void;
}

const FeedbackDetail: React.FC<FeedbackDetailProps> = ({ feedback, onClose }) => {
  // 格式化日期
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '暂无';

    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 渲染状态标签
  const renderStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '未处理';
      case 'processing':
        return '处理中';
      case 'replied':
        return '已回复';
      default:
        return '未知状态';
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-sm font-medium text-foreground">{feedback.title}</DialogTitle>
        </DialogHeader>

        <div className="mt-4 space-y-4">
          <div className="space-y-2">
            <div className="text-sm text-content-secondary">提交时间</div>
            <div className="text-sm text-foreground">{formatDate(feedback.createdAt)}</div>
          </div>

          <div className="space-y-2">
            <div className="text-sm text-content-secondary">状态</div>
            <div className="text-sm text-foreground">{renderStatusText(feedback.status)}</div>
          </div>

          <div className="space-y-2">
            <div className="text-sm text-content-secondary">问题描述</div>
            <div className="p-3 border-border bg-background-card rounded-md text-sm text-foreground border">
              {feedback.content}
            </div>
          </div>

          {feedback.status === 'replied' && feedback.adminReply && (
            <div className="space-y-2">
              <div className="text-sm font-medium text-blue-500">管理员回复 ({formatDate(feedback.replyAt)})</div>
              <div className="p-3 border-border bg-background-card rounded-md text-sm text-foreground border">
                {feedback.adminReply}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button onClick={onClose}>关闭</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FeedbackDetail;
