/**
 * 预测K线图系列组件
 * 
 * 该组件负责管理预测K线数据的加载、显示、更新和闪烁效果，
 * 从Chart.tsx中提取，作为独立的可复用组件
 */

import { useEffect, useRef, useCallback } from 'react';
import { IChartApi, ISeriesApi, Time, CandlestickData, Range } from 'lightweight-charts';
import { PredictionData, ProcessedPrediction } from '../../types/chartTypes';
import { 
  timeToNumber, 
  parseNumericValue,
  getCandleColor,
  getCandleBorderColor
} from '../../utils/chartUtils';

interface PredictionCandlestickSeriesProps {
  chartApi: IChartApi | null;
  isChartReady: boolean;
  predictionData: PredictionData[];
  showPrediction: boolean;
  onLoadMoreHistory?: (oldestTime: number) => void;
  predictionSeries?: ISeriesApi<"Candlestick"> | null; // 从图表管理器获取的预测K线系列
}

/**
 * 预测K线图系列组件
 * 负责管理预测K线数据和闪烁效果
 */
const PredictionCandlestickSeries: React.FC<PredictionCandlestickSeriesProps> = ({
  chartApi,
  isChartReady,
  predictionData,
  showPrediction,
  onLoadMoreHistory,
  predictionSeries
}) => {
  // 引用存储
  const predictionSeriesRef = useRef<ISeriesApi<"Candlestick"> | null>(null);
  const oldestLoadedTimeRef = useRef<number | null>(null);
  const isLoadingMoreRef = useRef<boolean>(false);
  const flashingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastPredictionUpdateRef = useRef<number>(0);

  // 初始化预测K线系列引用
  useEffect(() => {
    // 检查是否提供了预测K线系列
    if (!predictionSeries) {
      console.warn('未提供预测K线系列实例');
      return;
    }
      
      // 保存引用
      predictionSeriesRef.current = predictionSeries;
    console.log('使用图表管理器提供的预测K线系列');
    
    // 组件卸载时清理闪烁效果
    return () => {
      clearFlashingEffect();
    };
  }, [predictionSeries]);

  // 清理闪烁效果
  const clearFlashingEffect = useCallback(() => {
    if (flashingIntervalRef.current) {
      clearInterval(flashingIntervalRef.current);
      flashingIntervalRef.current = null;
    }
  }, []);

  // 预处理预测数据
  const processPredictionData = useCallback((data: PredictionData[], visibleRange?: Range<Time> | null): ProcessedPrediction[] => {
    if (!data.length) return [];
    
    // 处理原始数据，计算各种比例
    const processedData = data.map(item => {
      const open = parseNumericValue(item.open);
      const high = parseNumericValue(item.high);
      const low = parseNumericValue(item.low);
      const close = parseNumericValue(item.close);
      
      // 计算K线形态特征
      const isUp = close >= open;
      const bodyHeight = Math.abs(close - open);
      const upperShadow = isUp ? (high - close) : (high - open);
      const lowerShadow = isUp ? (open - low) : (close - low);
      const totalHeight = high - low;
      
      return {
        time: item.time,
        open, high, low, close,
        isActive: item.isActive,
        isUp,
        bodyHeight,
        upperShadow,
        lowerShadow,
        totalHeight
      };
    });
    
    // 筛选当前视图中的预测K线
    let inViewPredictions = processedData;
    if (visibleRange) {
      const rangeStart = timeToNumber(visibleRange.from);
      const rangeEnd = timeToNumber(visibleRange.to);
      
      const filtered = processedData.filter(item => {
        const itemTime = timeToNumber(item.time);
        return itemTime >= rangeStart && itemTime <= rangeEnd;
      });
      
      inViewPredictions = filtered.length > 0 ? filtered : processedData;
    }
    
    // 计算缩放因子
    const maxTotalHeightInView = Math.max(
      ...inViewPredictions.map(item => item.totalHeight),
      0.0001 // 防止除零错误
    );
    
    // 限制预测K线的最大高度，确保显示在底部30%区域
    const virtualMaxHeight = 20;
    const scaleFactor = virtualMaxHeight / maxTotalHeightInView;
    const minVisibleHeight = 0.2; // 最小可见高度
    
    // 确保预测K线的数值范围严格限制
    const maxAllowedValue = virtualMaxHeight / 2;
    const minAllowedValue = -maxAllowedValue;
    
    // 生成虚拟K线数据
    const formattedData = processedData.map(item => {
      // 计算虚拟OHLC值
      let virtualOpen, virtualClose, virtualHigh, virtualLow;
      const centerValue = 0; // 中线位置
      
      if (item.isUp) {
        // 上涨K线：实体从中线向上，下影线穿过中线
        virtualOpen = centerValue;
        virtualClose = centerValue + item.bodyHeight * scaleFactor;
        virtualHigh = virtualClose + item.upperShadow * scaleFactor;
        virtualLow = centerValue - item.lowerShadow * scaleFactor;
      } else {
        // 下跌K线：实体从中线向下，上影线穿过中线
        virtualClose = centerValue;
        virtualOpen = centerValue - item.bodyHeight * scaleFactor;
        virtualHigh = centerValue + item.upperShadow * scaleFactor;
        virtualLow = virtualOpen - item.lowerShadow * scaleFactor;
      }
      
      // 确保最小可见高度
      if (Math.abs(virtualClose - virtualOpen) < minVisibleHeight) {
        if (item.isUp) {
          virtualClose = virtualOpen + minVisibleHeight;
        } else {
          virtualOpen = virtualClose - minVisibleHeight;
        }
      }
      
      // 设置颜色
      const originalDirection = item.isUp ? 'up' : 'down' as const;
      const color = getCandleColor(originalDirection, 0.6);
      const borderColor = getCandleBorderColor(originalDirection, 0.8);
      const wickColor = borderColor;
      
      return {
        time: item.time,
        open: virtualOpen,
        high: virtualHigh,
        low: virtualLow,
        close: virtualClose,
        isActive: item.isActive,
        originalDirection,
        color,
        borderColor,
        wickColor
      } as ProcessedPrediction;
    });
    
    // 按时间排序
    return formattedData.sort((a, b) => timeToNumber(a.time) - timeToNumber(b.time));
  }, []);
  
  // 创建闪烁效果
  const createFlashingEffect = useCallback((activePrediction: ProcessedPrediction) => {
    if (!predictionSeriesRef.current || !activePrediction) return;
    
    // 清除之前的闪烁效果
    clearFlashingEffect();
    
    // 获取活跃K线的时间戳
    const activeTime = activePrediction.time;
    
    // 确保时间戳格式正确 - 转换为数字类型
    const normalizedTime = typeof activeTime === 'object' ? 
      (activeTime as any).timestamp || timeToNumber(activeTime) : 
      activeTime;
    
    // 设置闪烁参数
    let opacity = 0.5;
    let increasing = true;
    
    // 创建闪烁间隔
    flashingIntervalRef.current = setInterval(() => {
      if (!predictionSeriesRef.current) {
        clearFlashingEffect();
        return;
      }
      
      // 更新透明度
      if (increasing) {
        opacity += 0.05;
        if (opacity >= 1) {
          opacity = 1;
          increasing = false;
        }
      } else {
        opacity -= 0.05;
        if (opacity <= 0.3) {
          opacity = 0.3;
          increasing = true;
        }
      }
      
      // 设置闪烁颜色 - 使用工具函数
      const upColor = getCandleColor('up', opacity);
      const downColor = getCandleColor('down', opacity);
      const borderUpColor = getCandleBorderColor('up', 1);
      const borderDownColor = getCandleBorderColor('down', 1);
      
      try {
        // 更新活跃K线
        predictionSeriesRef.current.update({
          time: normalizedTime,
          open: activePrediction.open,
          high: activePrediction.high,
          low: activePrediction.low,
          close: activePrediction.close,
          color: activePrediction.originalDirection === 'up' ? upColor : downColor,
          borderColor: activePrediction.originalDirection === 'up' ? borderUpColor : borderDownColor,
          wickColor: activePrediction.originalDirection === 'up' ? borderUpColor : borderDownColor,
        });
      } catch (error) {
        console.error('更新闪烁效果失败:', error);
        clearFlashingEffect(); // 发生错误时停止闪烁效果
      }
    }, 50); //闪烁频率50毫秒
  }, [clearFlashingEffect]);

  // 更新预测数据
  useEffect(() => {
    if (!predictionSeriesRef.current || !chartApi || predictionData.length === 0 || !showPrediction) {
      return;
    }
    
    console.log('更新预测K线数据, 数据条数:', predictionData.length);
    lastPredictionUpdateRef.current = Date.now(); // 记录本次更新时间
    
    try {
      // 获取当前视图范围
      const visibleRange = chartApi.timeScale().getVisibleRange();
      
      // 处理预测数据 - 保持相同的缩放比例
      const processedPredictions = processPredictionData(predictionData, visibleRange);
      
      // 设置预测数据
      if (processedPredictions.length > 0) {
        predictionSeriesRef.current.setData(processedPredictions as CandlestickData[]);
        
        // 找到活跃的预测项
        const activePrediction = processedPredictions.find(item => item.isActive);
        if (activePrediction) {
          // 创建闪烁效果
          createFlashingEffect(activePrediction);
        }
        
        // 记录已加载数据中最早的时间戳
        if (predictionData.length > 0) {
          const timeValues = predictionData.map(item => 
            typeof item.time === 'string' ? parseInt(item.time) : Number(item.time)
          );
          oldestLoadedTimeRef.current = Math.min(...timeValues) * 1000; // 转换为毫秒
          console.log('当前加载的最早预测时间:', new Date(oldestLoadedTimeRef.current).toISOString());
        }
      } else {
        console.warn('预测数据处理后为空');
      }
    } catch (error) {
      console.error('更新预测K线数据时出错:', error);
    }
    
    // 清理函数
    return clearFlashingEffect;
  }, [predictionData, processPredictionData, createFlashingEffect, clearFlashingEffect, showPrediction, chartApi]);

  // 控制预测图表可见性
  useEffect(() => {
    if (!chartApi || !predictionSeriesRef.current) return;
    
    // 设置预测坐标轴可见性
    try {
      // 设置预测坐标轴不可见
      chartApi.priceScale('prediction').applyOptions({
        visible: showPrediction,
      });
      
      // 设置预测K线系列不可见（通过将数据设为空数组来隐藏）
      if (!showPrediction) {
        // 清除闪烁效果
        clearFlashingEffect();
        
        // 清空预测数据来完全隐藏预测K线
        predictionSeriesRef.current.setData([]);
        
        // 也可以设置不可见属性
        predictionSeriesRef.current.applyOptions({
          visible: false,
        });
      } else {
        // 如果需要显示，则恢复预测K线可见性
        predictionSeriesRef.current.applyOptions({
          visible: true,
        });
        
        // 如果有数据，更新数据
        if (predictionData.length > 0) {
          const visibleRange = chartApi.timeScale().getVisibleRange();
          const processedPredictions = processPredictionData(predictionData, visibleRange);
          predictionSeriesRef.current.setData(processedPredictions as CandlestickData[]);
        }
      }
    } catch (error) {
      console.error('设置预测坐标轴可见性失败:', error);
    }
  }, [showPrediction, clearFlashingEffect, predictionData, processPredictionData, chartApi]);

  // 创建时间范围变化处理函数 - 用于滚动加载更多历史数据
  const handleTimeRangeChange = useCallback((timeRange: Range<Time> | null) => {
    if (!timeRange || isLoadingMoreRef.current || !chartApi || !onLoadMoreHistory) return;
    
    // 检查是否滚动到了左边缘
    const visibleRangeStart = timeToNumber(timeRange.from);
    
    // 获取图表当前时间刻度的宽度
    const barSpacing = chartApi.timeScale().options().barSpacing || 6;
    const visibleTimeSpan = timeToNumber(timeRange.to) - visibleRangeStart;
    
    // 如果视图最左侧时间接近已加载数据的最早时间的10%，触发加载更多
    const leftEdgeThreshold = visibleTimeSpan * 0.1;
    const isNearLeftEdge = oldestLoadedTimeRef.current && 
        (visibleRangeStart * 1000 - leftEdgeThreshold <= oldestLoadedTimeRef.current);
        
    if (isNearLeftEdge && !isLoadingMoreRef.current && oldestLoadedTimeRef.current) {
      isLoadingMoreRef.current = true;
      console.log('已滚动到左边界，加载更早的预测数据...');
      
      // 调用回调函数加载更多数据
      onLoadMoreHistory(oldestLoadedTimeRef.current);
      
      // 防止频繁触发，设置短暂的冷却时间
      setTimeout(() => {
        isLoadingMoreRef.current = false;
      }, 2000);
    }
  }, [chartApi, onLoadMoreHistory]);

  // 监听时间轴变化，实现滚动加载历史预测数据
  useEffect(() => {
    if (!chartApi || !onLoadMoreHistory || !isChartReady) return;
    
    // 添加监听
    chartApi.timeScale().subscribeVisibleTimeRangeChange(handleTimeRangeChange);
    
    // 清理函数
    return () => {
      chartApi.timeScale().unsubscribeVisibleTimeRangeChange(handleTimeRangeChange);
    };
  }, [chartApi, isChartReady, handleTimeRangeChange, onLoadMoreHistory]);

  // 组件销毁时清理资源
  useEffect(() => {
    return () => {
      clearFlashingEffect();
    };
  }, [clearFlashingEffect]);

  // 组件不渲染任何UI元素
  return null;
};

export default PredictionCandlestickSeries;

 