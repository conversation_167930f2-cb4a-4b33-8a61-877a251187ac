import { Request, Response } from 'express';
import systemSettingsService from '../services/systemSettingsService';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

// 配置内存存储，用于处理文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 限制5MB
  },
  fileFilter: (req, file, cb) => {
    // 只允许上传图片
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp', 'image/x-icon'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的文件类型，仅支持图片文件'));
    }
  }
});

/**
 * 获取系统设置
 * GET /api/admin/settings
 */
export const getSystemSettings = async (req: Request, res: Response) => {
  try {
    const settings = await systemSettingsService.getSystemSettings();

    // 处理敏感信息脱敏
    let paymentSettings = null;
    if (settings.paymentSettings) {
      paymentSettings = {
        ...settings.paymentSettings,
        // 对API密钥进行脱敏处理
        apiKey: settings.paymentSettings.apiKey ?
          `${settings.paymentSettings.apiKey.substring(0, 4)}****${settings.paymentSettings.apiKey.slice(-4)}` : '',
        apiSecretKey: settings.paymentSettings.apiSecretKey ?
          `${settings.paymentSettings.apiSecretKey.substring(0, 4)}****${settings.paymentSettings.apiSecretKey.slice(-4)}` : ''
      };
    }

    res.json({
      success: true,
      settings: {
        siteInfo: settings.siteInfo,
        userSettings: settings.userSettings,
        subscriptionPrices: settings.subscriptionPrices,
        paymentSettings: paymentSettings,
        securitySettings: settings.securitySettings,
        updatedAt: settings.updatedAt
      }
    });
  } catch (error) {
    console.error('获取系统设置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 更新系统设置
 * PUT /api/admin/settings
 */
export const updateSystemSettings = async (req: Request, res: Response) => {
  try {
    const { siteInfo, userSettings, subscriptionPrices, paymentSettings: reqPaymentSettings, securitySettings } = req.body;

    // 验证请求体
    // 用户设置验证
    if (userSettings) {
      if (userSettings.trialDays !== undefined && (isNaN(userSettings.trialDays) || userSettings.trialDays < 0)) {
        return res.status(400).json({
          success: false,
          message: '试用天数必须是非负数'
        });
      }

      if (userSettings.inviteRewardDays !== undefined && (isNaN(userSettings.inviteRewardDays) || userSettings.inviteRewardDays < 0)) {
        return res.status(400).json({
          success: false,
          message: '邀请奖励天数必须是非负数'
        });
      }

      if (userSettings.passwordMinLength !== undefined && (isNaN(userSettings.passwordMinLength) || userSettings.passwordMinLength < 4)) {
        return res.status(400).json({
          success: false,
          message: '密码最小长度不能小于4'
        });
      }
    }

    // 订阅价格验证
    if (subscriptionPrices) {
      if (
        (subscriptionPrices.monthly !== undefined && (isNaN(subscriptionPrices.monthly) || subscriptionPrices.monthly < 0)) ||
        (subscriptionPrices.quarterly !== undefined && (isNaN(subscriptionPrices.quarterly) || subscriptionPrices.quarterly < 0)) ||
        (subscriptionPrices.yearly !== undefined && (isNaN(subscriptionPrices.yearly) || subscriptionPrices.yearly < 0))
      ) {
        return res.status(400).json({
          success: false,
          message: '订阅价格必须是非负数'
        });
      }
    }

    // 支付设置验证
    if (reqPaymentSettings) {
      if (reqPaymentSettings.failureLimit !== undefined && (isNaN(reqPaymentSettings.failureLimit) || reqPaymentSettings.failureLimit < 1)) {
        return res.status(400).json({
          success: false,
          message: '支付失败限制次数必须是正整数'
        });
      }

      if (reqPaymentSettings.failureLockTime !== undefined && (isNaN(reqPaymentSettings.failureLockTime) || reqPaymentSettings.failureLockTime < 1)) {
        return res.status(400).json({
          success: false,
          message: '支付失败锁定时间必须是正整数'
        });
      }
    }

    // 安全设置验证
    if (securitySettings) {
      if (securitySettings.binanceApiTimeout !== undefined && (isNaN(securitySettings.binanceApiTimeout) || securitySettings.binanceApiTimeout < 5000)) {
        return res.status(400).json({
          success: false,
          message: '币安API超时时间必须大于5000毫秒'
        });
      }

      if (securitySettings.binanceApiRetryTimes !== undefined && (isNaN(securitySettings.binanceApiRetryTimes) || securitySettings.binanceApiRetryTimes < 1)) {
        return res.status(400).json({
          success: false,
          message: '币安API重试次数必须是正整数'
        });
      }

      if (securitySettings.loginFailLimit !== undefined && (isNaN(securitySettings.loginFailLimit) || securitySettings.loginFailLimit < 1)) {
        return res.status(400).json({
          success: false,
          message: '登录失败限制次数必须是正整数'
        });
      }

      if (securitySettings.loginLockTime !== undefined && (isNaN(securitySettings.loginLockTime) || securitySettings.loginLockTime < 0.5)) {
        return res.status(400).json({
          success: false,
          message: '登录失败锁定时间必须大于0.5小时'
        });
      }
    }

    // 构建更新对象
    const updateData: any = {};
    if (siteInfo) updateData.siteInfo = siteInfo;
    if (userSettings) updateData.userSettings = userSettings;
    if (subscriptionPrices) updateData.subscriptionPrices = subscriptionPrices;
    if (reqPaymentSettings) updateData.paymentSettings = reqPaymentSettings;
    if (securitySettings) updateData.securitySettings = securitySettings;

    // 更新设置
    const userId = req.user?._id;
    const updatedSettings = await systemSettingsService.updateSystemSettings(updateData, userId?.toString());

    // 处理敏感信息脱敏
    let paymentSettings = null;
    if (updatedSettings.paymentSettings) {
      paymentSettings = {
        ...updatedSettings.paymentSettings,
        // 对API密钥进行脱敏处理
        apiKey: updatedSettings.paymentSettings.apiKey ?
          `${updatedSettings.paymentSettings.apiKey.substring(0, 4)}****${updatedSettings.paymentSettings.apiKey.slice(-4)}` : '',
        apiSecretKey: updatedSettings.paymentSettings.apiSecretKey ?
          `${updatedSettings.paymentSettings.apiSecretKey.substring(0, 4)}****${updatedSettings.paymentSettings.apiSecretKey.slice(-4)}` : ''
      };
    }

    res.json({
      success: true,
      settings: {
        siteInfo: updatedSettings.siteInfo,
        userSettings: updatedSettings.userSettings,
        subscriptionPrices: updatedSettings.subscriptionPrices,
        paymentSettings: paymentSettings,
        securitySettings: updatedSettings.securitySettings,
        updatedAt: updatedSettings.updatedAt
      }
    });
  } catch (error) {
    console.error('更新系统设置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 上传Logo图片
 * POST /api/admin/settings/upload-logo
 */
export const uploadLogo = async (req: Request, res: Response) => {
  // 使用multer中间件处理单个文件上传
  const uploadSingle = upload.single('logo');

  uploadSingle(req, res, async (err) => {
    if (err) {
      return res.status(400).json({
        success: false,
        message: err.message
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '未提供文件'
      });
    }

    try {
      // 保存文件并获取URL
      const logoUrl = await systemSettingsService.saveImageFile(
        req.file.buffer,
        req.file.originalname,
        'logo'
      );

      // 更新系统设置
      const settings = await systemSettingsService.getSystemSettings();
      settings.siteInfo.logoUrl = logoUrl;

      const userId = req.user?._id;
      await systemSettingsService.updateSystemSettings({ siteInfo: settings.siteInfo }, userId?.toString());

      res.json({
        success: true,
        logoUrl
      });
    } catch (error) {
      console.error('上传Logo错误:', error);
      res.status(500).json({
        success: false,
        message: '服务器错误，请稍后重试'
      });
    }
  });
};

/**
 * 上传Favicon图片
 * POST /api/admin/settings/upload-favicon
 */
export const uploadFavicon = async (req: Request, res: Response) => {
  // 使用multer中间件处理单个文件上传
  const uploadSingle = upload.single('favicon');

  uploadSingle(req, res, async (err) => {
    if (err) {
      return res.status(400).json({
        success: false,
        message: err.message
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '未提供文件'
      });
    }

    try {
      // 保存文件并获取URL
      const faviconUrl = await systemSettingsService.saveImageFile(
        req.file.buffer,
        req.file.originalname,
        'favicon'
      );

      // 更新系统设置
      const settings = await systemSettingsService.getSystemSettings();
      settings.siteInfo.faviconUrl = faviconUrl;

      const userId = req.user?._id;
      await systemSettingsService.updateSystemSettings({ siteInfo: settings.siteInfo }, userId?.toString());

      res.json({
        success: true,
        faviconUrl
      });
    } catch (error) {
      console.error('上传Favicon错误:', error);
      res.status(500).json({
        success: false,
        message: '服务器错误，请稍后重试'
      });
    }
  });
};

export default {
  getSystemSettings,
  updateSystemSettings,
  uploadLogo,
  uploadFavicon
};