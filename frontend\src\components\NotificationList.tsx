import React, { useState, useEffect, useCallback } from 'react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Trash2, Check, CheckCheck } from 'lucide-react';
import notificationService, { Notification } from '../services/notificationService';
import { useToast } from './ui/use-toast';

interface NotificationListProps {
  className?: string;
}

const NotificationList: React.FC<NotificationListProps> = ({ className = '' }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  // 完全移除loading状态，不显示任何加载动画
  // const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [unreadCount, setUnreadCount] = useState(0);
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);
  const { toast } = useToast();

  // 加载通知列表
  const loadNotifications = useCallback(async (pageNum: number = 1, unreadOnly: boolean = false) => {
    try {
      // 完全移除loading状态，不显示任何加载动画
      // setLoading(true);
      const response = await notificationService.getNotifications(pageNum, 10, unreadOnly);

      if (pageNum === 1) {
        setNotifications(response.data);
      } else {
        setNotifications(prev => [...prev, ...response.data]);
      }

      setPage(pageNum);
      setTotalPages(response.pagination.totalPages);
    } catch (error: any) {
      console.error('加载通知失败:', error);

      // 检查是否是认证错误
      if (error.response?.status === 401) {
        toast({
          title: '登录已过期，请重新登录',
          variant: 'destructive'
        });
        // 不在这里重定向，让拦截器处理
      } else if (error.response?.status === 404) {
        toast({
          title: '通知服务暂不可用',
          variant: 'destructive'
        });
      } else if (error.code === 'NETWORK_ERROR' || !error.response) {
        toast({
          title: '网络连接失败，请检查网络',
          variant: 'destructive'
        });
      } else {
        toast({
          title: '加载通知失败',
          description: error.response?.data?.message || error.message,
          variant: 'destructive'
        });
      }
    } finally {
      // setLoading(false);
    }
  }, [toast]);

  // 加载未读通知数量
  const loadUnreadCount = async () => {
    try {
      const count = await notificationService.getUnreadCount();
      setUnreadCount(count);
    } catch (error: any) {
      console.error('加载未读通知数量失败:', error);
      // 未读数量加载失败不显示错误提示，避免过多提示
      if (error.response?.status !== 401) {
        // 只有非认证错误才设置为0，认证错误让拦截器处理
        setUnreadCount(0);
      }
    }
  };

  // 标记通知为已读
  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await notificationService.markAsRead(notificationId);
      
      // 更新本地状态
      setNotifications(prev => 
        prev.map(notification => 
          notification._id === notificationId 
            ? { ...notification, isRead: true, readAt: new Date().toISOString() }
            : notification
        )
      );
      
      // 更新未读数量
      setUnreadCount(prev => Math.max(0, prev - 1));
      toast({
        title: '通知已标记为已读',
        variant: 'default'
      });
    } catch (error) {
      console.error('标记通知已读失败:', error);
      toast({
        title: '操作失败',
        variant: 'destructive'
      });
    }
  };

  // 标记所有通知为已读
  const handleMarkAllAsRead = async () => {
    try {
      await notificationService.markAllAsRead();
      
      // 更新本地状态
      setNotifications(prev => 
        prev.map(notification => ({ 
          ...notification, 
          isRead: true, 
          readAt: new Date().toISOString() 
        }))
      );
      
      setUnreadCount(0);
      toast({
        title: '所有通知已标记为已读',
        variant: 'default'
      });
    } catch (error) {
      console.error('标记所有通知已读失败:', error);
      toast({
        title: '操作失败',
        variant: 'destructive'
      });
    }
  };

  // 删除通知
  const handleDeleteNotification = async (notificationId: string) => {
    try {
      await notificationService.deleteNotification(notificationId);
      
      // 从本地状态中移除
      const deletedNotification = notifications.find(n => n._id === notificationId);
      setNotifications(prev => prev.filter(n => n._id !== notificationId));
      
      // 如果删除的是未读通知，更新未读数量
      if (deletedNotification && !deletedNotification.isRead) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
      
      toast({
        title: '通知已删除',
        variant: 'default'
      });
    } catch (error) {
      console.error('删除通知失败:', error);
      toast({
        title: '删除失败',
        variant: 'destructive'
      });
    }
  };

  // 加载更多通知
  const handleLoadMore = () => {
    if (page < totalPages) {
      loadNotifications(page + 1, showUnreadOnly);
    }
  };

  // 切换显示模式
  const handleToggleUnreadOnly = (unreadOnly: boolean) => {
    setShowUnreadOnly(unreadOnly);
    setPage(1);
    loadNotifications(1, unreadOnly);
  };

  // 初始化加载
  useEffect(() => {
    loadNotifications();
    loadUnreadCount();
  }, [loadNotifications]);

  // 完全隐藏加载状态，不显示任何加载动画
  // if (loading && notifications.length === 0) {
  //   return (
  //     <div className={`space-y-4 ${className}`}>
  //       <div className="animate-pulse space-y-3">
  //         {[1, 2, 3].map(i => (
  //           <div key={i} className="h-20 bg-muted rounded-lg"></div>
  //         ))}
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            variant={!showUnreadOnly ? "default" : "outline"}
            size="sm"
            onClick={() => handleToggleUnreadOnly(false)}
          >
            全部通知
          </Button>
          <Button
            variant={showUnreadOnly ? "default" : "outline"}
            size="sm"
            onClick={() => handleToggleUnreadOnly(true)}
          >
            未读通知 {unreadCount > 0 && <Badge variant="destructive" className="ml-1">{unreadCount}</Badge>}
          </Button>
        </div>
        
        {unreadCount > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleMarkAllAsRead}
            className="flex items-center space-x-1"
          >
            <CheckCheck className="h-4 w-4" />
            <span>全部已读</span>
          </Button>
        )}
      </div>

      {/* 通知列表 */}
      {notifications.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-content-secondary">
            {showUnreadOnly ? '没有未读通知' : '暂无通知'}
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {notifications.map((notification) => (
            <div
              key={notification._id}
              className={`p-4 rounded-lg border transition-colors ${
                notification.isRead 
                  ? 'bg-background border-border' 
                  : 'bg-primary/5 border-primary/20'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg">
                      {notificationService.getTypeIcon(notification.type)}
                    </span>
                    <h4 className="font-medium text-content-primary truncate">
                      {notification.title}
                    </h4>
                    {!notification.isRead && (
                      <Badge variant="destructive" className="text-xs">未读</Badge>
                    )}
                  </div>
                  
                  <p className="text-sm text-content-secondary mb-2 leading-relaxed">
                    {notification.content}
                  </p>
                  
                  <p className="text-xs text-content-muted">
                    {notificationService.formatTime(notification.createdAt)}

                  </p>
                </div>
                
                <div className="flex items-center space-x-1 ml-4">
                  {!notification.isRead && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleMarkAsRead(notification._id)}
                      className="h-8 w-8 p-0"
                      title="标记为已读"
                    >
                      <Check className="h-4 w-4" />
                    </Button>
                  )}
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteNotification(notification._id)}
                    className="h-8 w-8 p-0 text-error hover:text-error"
                    title="删除通知"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 加载更多按钮 */}
      {page < totalPages && (
        <div className="text-center">
          <Button
            variant="outline"
            onClick={handleLoadMore}
          >
            加载更多
          </Button>
        </div>
      )}
    </div>
  );
};

export default NotificationList;
