import { Request, Response } from 'express';
import predictionService from '../services/predictionService';

/**
 * 预测数据控制器类
 * 处理与预测数据相关的HTTP请求
 */
class PredictionController {
  /**
   * 获取最近的预测数据
   * 获取并返回指定交易对的最近预测数据
   * 
   * @param req HTTP请求对象
   * @param res HTTP响应对象
   */
  async getRecentPredictions(req: Request, res: Response): Promise<void> {
    try {
      // 获取查询参数，设置默认值
      const symbol = req.query.symbol as string || 'BTCUSDT';
      
      // 确保limit是有效的数字，默认值300
      const limit = req.query.limit ? 
        Math.min(1000, Math.max(1, parseInt(req.query.limit as string))) : 300;
      
      const endTime = req.query.endTime ? parseInt(req.query.endTime as string) : undefined;

      // 获取预测数据
      const predictions = await predictionService.getRecentPredictions(symbol, limit, endTime);
      
      // 转换为前端所需格式
      const formattedPredictions = predictions.map(prediction => ({
        time: parseInt(String(prediction.targetStartTime)) / 1000,  // 转换为秒级时间戳
        open: parseFloat(prediction.open),                          // 转换为数字
        high: parseFloat(prediction.high),
        low: parseFloat(prediction.low),
        close: parseFloat(prediction.close),
        isActive: prediction.isActive,                              // 是否为活跃预测
        targetStartTime: prediction.targetStartTime,                // 目标开始时间
        targetEndTime: prediction.targetEndTime,                    // 目标结束时间
        predictionTime: prediction.predictionTime                   // 预测生成时间
      }));
      
      // 返回JSON格式响应
      res.json(formattedPredictions);
    } catch (error) {
      console.error('获取预测数据时出错:', error);
      res.status(500).json({ error: '获取预测数据失败' });
    }
  }

  /**
   * 手动触发预测生成
   * 立即生成新的预测数据，无论当前时间是否为预定义的预测触发时间
   * 
   * @param req HTTP请求对象
   * @param res HTTP响应对象
   */
  async triggerPrediction(req: Request, res: Response): Promise<void> {
    try {
      // 获取查询参数，设置默认值
      const symbol = req.query.symbol as string || 'BTCUSDT';
      const currentTime = new Date();
      
      // 生成预测
      const prediction = await predictionService.generatePrediction(currentTime, symbol);
      
      if (prediction) {
        // 预测生成成功，返回成功响应和预测数据
        res.json({ 
          success: true, 
          message: '预测生成成功',
          prediction: {
            time: parseInt(String(prediction.targetStartTime)) / 1000,
            open: parseFloat(prediction.open),
            high: parseFloat(prediction.high),
            low: parseFloat(prediction.low),
            close: parseFloat(prediction.close),
            isActive: prediction.isActive,
            targetStartTime: prediction.targetStartTime,
            targetEndTime: prediction.targetEndTime,
            predictionTime: prediction.predictionTime
          }
        });
      } else {
        // 预测生成失败，返回错误响应
        res.status(400).json({ 
          success: false, 
          message: '无法生成预测。当前时间可能不是有效的预测触发时间。' 
        });
      }
    } catch (error) {
      console.error('触发预测生成时出错:', error);
      res.status(500).json({ error: '生成预测失败' });
    }
  }
}

// 导出控制器实例
export default new PredictionController(); 