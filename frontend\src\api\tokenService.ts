import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000/api';

// 内存中存储 accessToken，不持久化到 localStorage
let inMemoryToken: string | null = null;

// 是否正在刷新令牌的标志
let isRefreshing = false;

// 等待令牌刷新的请求队列
let refreshSubscribers: Array<(token: string) => void> = [];

/**
 * 设置访问令牌到内存
 */
export const setToken = (token: string | null) => {
  inMemoryToken = token;
};

/**
 * 获取当前内存中的访问令牌
 */
export const getToken = (): string | null => {
  return inMemoryToken;
};

/**
 * 从 localStorage 加载初始令牌到内存
 */
export const loadTokenFromStorage = () => {
  try {
    const userStorage = localStorage.getItem('user-storage');
    if (userStorage) {
      const parsedStorage = JSON.parse(userStorage);
      const token = parsedStorage.state?.token;
      
      if (token) {
        inMemoryToken = token;
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error('从 localStorage 解析 token 失败:', error);
    return false;
  }
};

/**
 * 更新 localStorage 中的令牌
 */
export const updateStorageToken = (newToken: string) => {
  try {
    const userStorage = localStorage.getItem('user-storage');
    if (userStorage) {
      const parsedStorage = JSON.parse(userStorage);
      
      if (parsedStorage.state) {
        parsedStorage.state.token = newToken;
        localStorage.setItem('user-storage', JSON.stringify(parsedStorage));
      }
    }
  } catch (error) {
    console.error('更新 localStorage 中的 token 失败:', error);
  }
};

/**
 * 添加请求到刷新订阅队列
 */
export const subscribeTokenRefresh = (callback: (token: string) => void) => {
  refreshSubscribers.push(callback);
};

/**
 * 通知所有等待的请求刷新完成
 */
export const onTokenRefreshed = (newToken: string) => {
  refreshSubscribers.forEach(callback => callback(newToken));
  refreshSubscribers = [];
};

/**
 * 创建一个自定义事件，用于通知 Session 过期
 */
export const notifySessionExpired = () => {
  const event = new CustomEvent('unauthorized', {
    detail: { reason: 'session_expired' }
  });
  window.dispatchEvent(event);
};

/**
 * 刷新访问令牌
 */
export const refreshToken = async (): Promise<string | null> => {
  try {
    if (isRefreshing) {
      // 返回一个Promise，当令牌刷新后会被解析
      return new Promise<string>((resolve) => {
        subscribeTokenRefresh(token => {
          resolve(token);
        });
      });
    }
    
    isRefreshing = true;
    
    // 请求新的访问令牌
    const response = await axios.post(
      `${API_URL}/auth/refresh`,
      {},
      { withCredentials: true } // 确保发送和接收cookie
    );
    
    if (response.data.success && response.data.accessToken) {
      const newToken = response.data.accessToken;
      setToken(newToken);
      updateStorageToken(newToken);
      onTokenRefreshed(newToken);
      
      isRefreshing = false;
      return newToken;
    } else {
      // 刷新失败，清除令牌
      setToken(null);
      isRefreshing = false;
      // 通知应用 session 已过期
      notifySessionExpired();
      return null;
    }
  } catch (error) {
    console.error('刷新令牌失败:', error);
    setToken(null);
    isRefreshing = false;
    // 通知应用 session 已过期
    notifySessionExpired();
    return null;
  }
};

export default {
  setToken,
  getToken,
  loadTokenFromStorage,
  refreshToken,
  updateStorageToken,
  notifySessionExpired
};