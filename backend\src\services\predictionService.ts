import binanceService from './binanceService';
import Prediction from '../models/Prediction';
import { IPrediction } from '../models/Prediction';

/**
 * 预测服务类
 * 负责生成BTC价格预测，基于不同时间点的1分钟K线预测未来30分钟走势
 */
class PredictionService {
  /**
   * 生成预测K线的核心方法
   * 根据特定时间生成BTC价格预测
   * 
   * @param currentTime 当前时间对象，用于确定预测触发点
   * @param symbol 交易对符号，默认为BTCUSDT
   * @returns 返回生成的预测数据，如果不是有效预测时间则返回null
   */
  async generatePrediction(currentTime: Date, symbol: string = 'BTCUSDT'): Promise<IPrediction | null> {
    try {
      const hour = currentTime.getUTCHours();    // 改用UTC时间
      const minute = currentTime.getUTCMinutes(); // 改用UTC时间
      
      console.log(`[预测服务][${currentTime.toISOString()}] 开始执行预测任务，小时: ${hour}, 分钟: ${minute}`);
      
      // 验证是否为指定的预测触发时间
      if (!this.isValidPredictionTime(hour, minute)) {
        console.log(`[预测服务] 不是有效的预测时间: ${hour}:${minute}`);
        return null;
      }

      // 获取预测的目标时间段和源K线时间
      const { sourceKlineTime, targetStartTime, targetEndTime } = this.calculateTimes(currentTime);
      
      console.log(`[预测服务] 计算出的时间参数: 源K线时间=${new Date(sourceKlineTime).toISOString()}, ` +
                  `目标开始=${new Date(targetStartTime).toISOString()}, ` + 
                  `目标结束=${new Date(targetEndTime).toISOString()}`);
      
      // 从币安API获取指定的1分钟K线数据
      const sourceKline = await binanceService.getMinuteKlineByTime(symbol, sourceKlineTime);
      
      if (!sourceKline) {
        throw new Error(`未找到源K线数据，时间: ${sourceKlineTime}`);
      }
      
      console.log(`[预测服务] 成功获取源K线数据: 开盘=${sourceKline.open}, 收盘=${sourceKline.close}`);
      
      // 检查数据库中是否已存在相同时间段的预测
      const existingPrediction = await Prediction.findOne({
        symbol,
        targetStartTime
      });
      
      if (existingPrediction) {
        // 修改为使用ISO格式显示UTC时间，避免本地时间转换
        console.log(`[预测服务] 已存在相同时间段的预测，跳过: ${new Date(targetStartTime).toISOString()} - ${new Date(targetEndTime).toISOString()}`);
        return existingPrediction;
      }
      
      // 生成预测K线
      const prediction = this.createPredictionFromKline(
        symbol,
        currentTime.getTime(),
        targetStartTime,
        targetEndTime,
        sourceKlineTime,
        sourceKline
      );
      
      // 将之前的活跃预测设为非活跃
      await this.deactivatePreviousPredictions(symbol);
      
      // 保存新预测
      const savedPrediction = await prediction.save();
      console.log(`[预测服务] 成功生成预测，目标时间段: ${new Date(targetStartTime).toISOString()} - ${new Date(targetEndTime).toISOString()}`);
      
      return savedPrediction;
    } catch (error) {
      console.error('[预测服务] 生成预测时出错:', error);
      return null;
    }
  }

  /**
   * 检查是否为有效的预测触发时间
   * 根据小时和分钟判断当前是否为预测触发时间点
   * 
   * @param hour 小时(0-23)
   * @param minute 分钟(0-59)
   * @returns 是否为有效的预测时间
   */
  private isValidPredictionTime(hour: number, minute: number): boolean {
    const isOddHour = hour % 2 === 1;
    const paddedMinute = minute.toString().padStart(2, '0');
    
    // 奇数小时：02分、28分、58分触发预测
    // 偶数小时：28分触发预测
    if (isOddHour) {
      return ['02', '28', '58'].includes(paddedMinute);
    } else {
      return ['28'].includes(paddedMinute);
    }
  }

/**
 * 计算预测所需的时间参数（基于 UTC 时间，其他逻辑保持不变）
 * 
 * @param currentTime 当前时间对象
 * @returns 包含源K线时间、目标开始时间和结束时间的对象
 */
private calculateTimes(currentTime: Date): { sourceKlineTime: number, targetStartTime: number, targetEndTime: number } {
  const hour = currentTime.getUTCHours();    // 使用 UTC 小时
  const minute = currentTime.getUTCMinutes(); // 使用 UTC 分钟
  const isOddHour = hour % 2 === 1;

  let sourceKlineTime: number;
  let targetStartTime: number;
  let targetEndTime: number;

  // 使用let而不是const允许后续重新赋值
  let workDate = new Date(currentTime);
  workDate.setUTCSeconds(0);
  workDate.setUTCMilliseconds(0);

  if (isOddHour) {
    if (minute === 2) {
      workDate.setUTCMinutes(0);
      sourceKlineTime = workDate.getTime();
      targetStartTime = sourceKlineTime;
      workDate.setUTCMinutes(30);
      targetEndTime = workDate.getTime();
    } else if (minute === 28) {
      workDate.setUTCMinutes(1);
      sourceKlineTime = workDate.getTime();
      workDate.setUTCMinutes(30);
      targetStartTime = workDate.getTime();
      workDate.setUTCHours(hour + 1);
      workDate.setUTCMinutes(0);
      targetEndTime = workDate.getTime();
    } else if (minute === 58) {
      workDate.setUTCMinutes(2);
      sourceKlineTime = workDate.getTime();
      workDate.setUTCHours(hour + 1);
      workDate.setUTCMinutes(0);
      targetStartTime = workDate.getTime();
      workDate.setUTCMinutes(30);
      targetEndTime = workDate.getTime();
    } else {
      throw new Error(`无效的奇数小时分钟值（UTC）: ${minute}`);
    }
  } else {
    if (minute === 28) {
      // 处理特殊情况：如果是0点28分，需要处理前一天的23:03
      if (hour === 0) {
        // 创建前一天的时间对象
        const prevDay = new Date(workDate.getTime());
        prevDay.setUTCDate(prevDay.getUTCDate() - 1);
        prevDay.setUTCHours(23);
        prevDay.setUTCMinutes(3);
        sourceKlineTime = prevDay.getTime();
      } else {
        workDate.setUTCHours(hour - 1);
        workDate.setUTCMinutes(3);
        sourceKlineTime = workDate.getTime();
      }
      
      // 重设工作日期为当前日期
      workDate = new Date(currentTime);
      workDate.setUTCSeconds(0);
      workDate.setUTCMilliseconds(0);
      
      workDate.setUTCMinutes(30);
      targetStartTime = workDate.getTime();
      
      workDate.setUTCHours(hour + 1);
      workDate.setUTCMinutes(0);
      targetEndTime = workDate.getTime();
    } else {
      throw new Error(`无效的偶数小时分钟值（UTC）: ${minute}`);
    }
  }

  return { sourceKlineTime, targetStartTime, targetEndTime };
}

  /**
   * 将源K线的形态转化为预测K线
   * 基于源1分钟K线生成30分钟预测K线
   * 
   * @param symbol 交易对符号
   * @param predictionTime 预测生成时间
   * @param targetStartTime 目标开始时间
   * @param targetEndTime 目标结束时间
   * @param sourceKlineTime 源K线时间
   * @param sourceKline 源K线数据
   * @returns 创建的预测对象
   */
  private createPredictionFromKline(
    symbol: string,
    predictionTime: number,
    targetStartTime: number,
    targetEndTime: number,
    sourceKlineTime: number,
    sourceKline: any
  ): IPrediction {
    // 创建预测对象
    // 这里直接使用源K线的价格作为预测值
    // 在实际应用中，可以添加更复杂的算法和调整因子来提高预测准确性
    
    const prediction = new Prediction({
      symbol,                // 交易对符号
      predictionTime,        // 预测生成时间
      targetStartTime,       // 目标开始时间
      targetEndTime,         // 目标结束时间
      sourceKlineTime,       // 源K线时间
      open: sourceKline.open,   // 开盘价
      high: sourceKline.high,   // 最高价
      low: sourceKline.low,     // 最低价
      close: sourceKline.close, // 收盘价
      isActive: true            // 标记为活跃预测
    });
    
    return prediction;
  }

  /**
   * 将之前的活跃预测设为非活跃
   * 在生成新预测前将所有现有活跃预测标记为非活跃
   * 
   * @param symbol 交易对符号
   */
  private async deactivatePreviousPredictions(symbol: string): Promise<void> {
    await Prediction.updateMany(
      { symbol, isActive: true },
      { $set: { isActive: false } }
    );
  }

  /**
   * 获取最近的预测数据
   * 从数据库获取指定数量的最近预测
   * 
   * @param symbol 交易对符号，默认为BTCUSDT
   * @param limit 返回预测数量，默认为300
   * @param endTime 结束时间戳(毫秒)，如果提供则获取该时间戳之前的数据
   * @returns 返回预测数据数组
   */
  async getRecentPredictions(symbol: string = 'BTCUSDT', limit: number = 300, endTime?: number): Promise<IPrediction[]> {
    try {
      // 构建查询条件
      let query: any = { symbol };
      
      // 如果提供了endTime参数，获取该时间之前的数据
      if (endTime) {
        query.targetStartTime = { $lt: endTime };
      }
      
      // 获取预测数据 - 不再使用3天时间限制，只用limit参数控制返回数量
      return await Prediction.find(query)
        .sort({ targetStartTime: -1 })  // 按目标开始时间降序排序
        .limit(limit)
        .exec();
    } catch (error) {
      console.error('[预测服务] 获取最近预测数据时出错:', error);
      throw error;
    }
  }

  /**
   * 检查最近3天的预测K线并补齐缺失的数据
   * 寻找预期应有但实际缺失的预测K线，并按照预测规则补齐
   * 
   * @param symbol 交易对符号，默认为BTCUSDT
   * @returns 返回补齐的预测数据数组
   */
  async checkAndFillMissingPredictions(symbol: string = 'BTCUSDT'): Promise<IPrediction[]> {
    try {
      console.log(`[预测服务][${new Date().toISOString()}] 开始检查最近3天的预测K线数据完整性`);
      
      // 获取当前时间和3天前的时间 (使用UTC时间)
      const now = new Date();
      const threeDaysAgo = new Date(now);
      // 使用UTC日期设置以确保日期计算准确
      threeDaysAgo.setUTCDate(threeDaysAgo.getUTCDate() - 3);
      
      // 生成这3天内应该存在的所有预测时间点
      const expectedPredictionTimes = this.generateExpectedPredictionTimes(threeDaysAgo, now);
      console.log(`[预测服务] 计算得到3天内应有${expectedPredictionTimes.length}个预测时间点`);
      
      // 查询这3天内实际存在的预测数据
      const threeDaysAgoTimestamp = threeDaysAgo.getTime();
      const existingPredictions = await Prediction.find({
        symbol,
        targetStartTime: { $gte: threeDaysAgoTimestamp }
      }).sort({ targetStartTime: 1 });
      
      console.log(`[预测服务] 数据库中存在${existingPredictions.length}个预测记录`);
      
      // 找出缺失的预测时间点
      const existingTimeMap = new Map(existingPredictions.map(p => [p.targetStartTime, p]));
      const missingTimes = expectedPredictionTimes.filter(timeData => !existingTimeMap.has(timeData.targetStartTime));
      
      console.log(`[预测服务] 发现${missingTimes.length}个缺失的预测时间点，准备补齐`);
      
      // 补齐缺失的预测数据
      const filledPredictions: IPrediction[] = [];
      for (const timeData of missingTimes) {
        try {
          // 直接获取源K线数据而不检查当前时间
          const { sourceKlineTime } = timeData;
          
          // 从币安API获取指定的1分钟K线数据
          const sourceKline = await binanceService.getMinuteKlineByTime(symbol, sourceKlineTime);
          
          if (!sourceKline) {
            console.warn(`[预测服务] 未找到源K线数据，跳过时间点: ${new Date(timeData.targetStartTime).toISOString()}`);
            continue;
          }
          
          // 生成预测K线
          const prediction = this.createPredictionFromKline(
            symbol,
            timeData.predictionTime,
            timeData.targetStartTime,
            timeData.targetEndTime,
            sourceKlineTime,
            sourceKline
          );
          
          // 在保存前检查是否已存在相同symbol和targetStartTime的记录
          const existingRecord = await Prediction.findOne({
            symbol,
            targetStartTime: timeData.targetStartTime
          });
          
          // 如果记录已存在，跳过该条预测
          if (existingRecord) {
            console.log(`[预测服务] 已存在相同时间段的预测记录，跳过: ${new Date(timeData.targetStartTime).toISOString()}`);
            continue;
          }
          
          // 保存新预测，但不标记为活跃
          prediction.isActive = false;
          const savedPrediction = await prediction.save();
          
          console.log(`[预测服务] 成功补齐预测，目标时间段: ${new Date(savedPrediction.targetStartTime).toISOString()} - ${new Date(savedPrediction.targetEndTime).toISOString()}`);
          
          filledPredictions.push(savedPrediction);
        } catch (error) {
          console.error(`[预测服务] 补齐预测数据时出错，时间点: ${new Date(timeData.targetStartTime).toISOString()}`, error);
        }
      }
      
      console.log(`[预测服务] 成功补齐${filledPredictions.length}个预测记录`);
      return filledPredictions;
    } catch (error) {
      console.error('[预测服务] 检查并补齐预测数据时出错:', error);
      return [];
    }
  }
  
  /**
   * 生成指定时间范围内应有的所有预测时间点
   * 根据预测规则计算给定时间范围内所有应该存在的预测
   * 
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @returns 返回包含预测时间信息的数组
   */
  private generateExpectedPredictionTimes(startDate: Date, endDate: Date): {
    predictionTime: number,
    sourceKlineTime: number,
    targetStartTime: number,
    targetEndTime: number
  }[] {
    const result: {
      predictionTime: number,
      sourceKlineTime: number,
      targetStartTime: number,
      targetEndTime: number
    }[] = [];
    
    // 克隆开始日期，避免修改原始对象
    const currentDate = new Date(startDate);
    
    // 设置为当天的00:00 UTC
    currentDate.setUTCHours(0, 0, 0, 0);
    
    // 遍历每一天
    while (currentDate <= endDate) {
      // 使用日期的副本进行遍历
      const baseDate = new Date(currentDate);
      
      // 遍历每个小时
      for (let hour = 0; hour < 24; hour++) {
        const isOddHour = hour % 2 === 1;
        
        // 确定当前小时的预测触发分钟
        const predictMinutes = isOddHour ? [2, 28, 58] : [28];
        
        for (const minute of predictMinutes) {
          // 设置检查时间点 (使用UTC时间)
          const checkTime = new Date(baseDate);
          checkTime.setUTCHours(hour, minute, 0, 0);
          
          // 跳过未来的时间点
          if (checkTime > endDate) continue;
          
          // 跳过过去3天以外的时间点
          if (checkTime < startDate) continue;
          
          // 计算该时间点对应的预测时间参数
          // 重要：为每个时间点创建一个全新的日期对象，避免时间累积错误
          let workDate = new Date(checkTime);
          let predictionSourceKlineTime: number = 0;
          let predictionTargetStartTime: number = 0;
          let predictionTargetEndTime: number = 0;
          
          if (isOddHour) {
            if (minute === 2) {
              // 奇数小时02分：使用HH:00的K线预测HH:00-HH:30
              workDate.setUTCMinutes(0);
              predictionSourceKlineTime = workDate.getTime();
              predictionTargetStartTime = predictionSourceKlineTime;
              
              // 重新创建日期对象避免累积错误
              workDate = new Date(checkTime);
              workDate.setUTCMinutes(30);
              predictionTargetEndTime = workDate.getTime();
            } else if (minute === 28) {
              // 奇数小时28分：使用HH:01的K线预测HH:30-HH+1:00
              workDate.setUTCMinutes(1);
              predictionSourceKlineTime = workDate.getTime();
              
              // 重新创建日期对象避免累积错误
              workDate = new Date(checkTime);
              workDate.setUTCMinutes(30);
              predictionTargetStartTime = workDate.getTime();
              
              // 重新创建日期对象并设置为下一小时
              workDate = new Date(checkTime);
              workDate.setUTCHours(hour + 1);
              workDate.setUTCMinutes(0);
              predictionTargetEndTime = workDate.getTime();
            } else if (minute === 58) {
              // 奇数小时58分：使用HH:02的K线预测HH+1:00-HH+1:30
              workDate.setUTCMinutes(2);
              predictionSourceKlineTime = workDate.getTime();
              
              // 重新创建日期对象并设置为下一小时
              workDate = new Date(checkTime);
              workDate.setUTCHours(hour + 1);
              workDate.setUTCMinutes(0);
              predictionTargetStartTime = workDate.getTime();
              
              // 重新创建日期对象并设置为下一小时30分
              workDate = new Date(checkTime);
              workDate.setUTCHours(hour + 1);
              workDate.setUTCMinutes(30);
              predictionTargetEndTime = workDate.getTime();
            }
          } else {
            if (minute === 28) {
              // 偶数小时28分：使用HH-1:03的K线预测HH:30-HH+1:00
              // 重要：对于hour=0的情况，需要特殊处理前一天
              let sourceDate = new Date(checkTime);
              if (hour === 0) {
                // 如果是0点，则使用前一天的23:03
                // 为避免日期问题，使用UTC时间，明确设置日期为前一天
                const prevDay = new Date(sourceDate.getTime());
                prevDay.setUTCDate(prevDay.getUTCDate() - 1);
                prevDay.setUTCHours(23);
                prevDay.setUTCMinutes(3);
                predictionSourceKlineTime = prevDay.getTime();
              } else {
                // 否则使用当天的前一小时
                sourceDate.setUTCHours(hour - 1);
                sourceDate.setUTCMinutes(3);
                predictionSourceKlineTime = sourceDate.getTime();
              }
              
              // 当前时间的当前小时30分
              workDate = new Date(checkTime);
              workDate.setUTCMinutes(30);
              predictionTargetStartTime = workDate.getTime();
              
              // 当前时间的下一小时00分
              workDate = new Date(checkTime);
              workDate.setUTCHours(hour + 1);
              workDate.setUTCMinutes(0);
              predictionTargetEndTime = workDate.getTime();
            }
          }
          
          const predictionTime = checkTime.getTime();
          const sourceKlineTime = predictionSourceKlineTime;
          const targetStartTime = predictionTargetStartTime;
          const targetEndTime = predictionTargetEndTime;
          
          // 将计算出的时间点添加到结果中
          result.push({
            predictionTime,
            sourceKlineTime,
            targetStartTime,
            targetEndTime
          });
        }
      }
      
      // 前进到下一天
      currentDate.setUTCDate(currentDate.getUTCDate() + 1);
    }
    
    return result;
  }
}

// 导出服务实例
export default new PredictionService(); 