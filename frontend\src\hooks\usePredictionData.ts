/**
 * 预测数据获取和管理Hook
 * 
 * 该Hook负责预测数据的获取、更新和历史数据加载
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { fetchPredictions } from '../services/api';
import { PredictionData } from '../types/chartTypes';

interface PredictionDataState {
  data: PredictionData[];
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
}

/**
 * 预测数据获取和管理Hook
 * @param refreshInterval 数据刷新间隔(毫秒)
 * @returns 预测数据状态和操作方法
 */
export const usePredictionData = (refreshInterval: number = 60000) => {
  // 状态管理
  const [state, setState] = useState<PredictionDataState>({
    data: [],
    isLoading: true,
    isLoadingMore: false,
    error: null
  });

  // 引用存储
  const isRefreshingRef = useRef<boolean>(false);

  // 初始加载预测数据
  const loadData = useCallback(async () => {
    if (isRefreshingRef.current) return;
    
    isRefreshingRef.current = true;
    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      const predictionsResponse = await fetchPredictions();
      if (predictionsResponse && Array.isArray(predictionsResponse) && predictionsResponse.length > 0) {
        console.log(`获取到 ${predictionsResponse.length} 条预测数据`);
        setState(prev => ({ 
          ...prev, 
          data: predictionsResponse,
          error: null
        }));
      } else {
        console.warn('获取到的预测数据为空或无效');
      }
    } catch (err) {
      console.error('预测数据加载失败:', err);
      setState(prev => ({ 
        ...prev, 
        error: '获取预测数据失败' 
      }));
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
      isRefreshingRef.current = false;
    }
  }, []);

  // 刷新预测数据
  const refreshData = useCallback(async () => {
    if (isRefreshingRef.current) return;
    
    isRefreshingRef.current = true;
    
    try {
      // 只获取最新的预测数据（不带endTime参数）
      const latestPredictions = await fetchPredictions();
      if (latestPredictions && Array.isArray(latestPredictions) && latestPredictions.length > 0) {
        console.log(`刷新获取到 ${latestPredictions.length} 条预测数据`);
        
        // 合并新旧数据，保留历史数据
        setState(prev => {
          // 创建一个Map用于去重，保持已有数据
          const uniqueMap = new Map();
          
          // 先处理当前所有数据（保留历史数据）
          prev.data.forEach(item => {
            uniqueMap.set(item.time.toString(), item);
          });
          
          // 再处理新数据，只有新数据会覆盖旧数据中的同时间点
          latestPredictions.forEach(item => {
            uniqueMap.set(item.time.toString(), item);
          });
          
          // 转换为数组并排序
          const combinedData = Array.from(uniqueMap.values()) as PredictionData[];
          combinedData.sort((a, b) => {
            const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
            const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
            return timeA - timeB;
          });
          
          console.log(`刷新合并后共有 ${combinedData.length} 条预测数据`);
          return { 
            ...prev, 
            data: combinedData,
            error: null
          };
        });
      }
    } catch (err) {
      console.error('定时刷新预测数据失败:', err);
    } finally {
      isRefreshingRef.current = false;
    }
  }, []);

  // 加载更多历史预测数据
  const loadMoreHistory = useCallback(async (oldestTime: number) => {
    setState(prev => {
      if (prev.isLoadingMore) return prev;
      return { ...prev, isLoadingMore: true };
    });
    
    console.log(`加载更早的预测数据，时间戳: ${oldestTime}, ${new Date(oldestTime).toISOString()}`);
    
    try {
      // 获取指定时间之前的预测数据
      const olderPredictions = await fetchPredictions(oldestTime);
      
      if (olderPredictions && Array.isArray(olderPredictions) && olderPredictions.length > 0) {
        console.log(`获取到 ${olderPredictions.length} 条更早的预测数据`);
        
        // 合并新旧数据并去重
        setState(prev => {
          // 创建一个Map用于去重
          const uniqueMap = new Map();
          
          // 先处理当前数据
          prev.data.forEach(item => {
            uniqueMap.set(item.time.toString(), item);
          });
          
          // 再处理新数据，有相同时间则新数据覆盖旧数据
          olderPredictions.forEach(item => {
            uniqueMap.set(item.time.toString(), item);
          });
          
          // 转换为数组并排序
          const combinedData = Array.from(uniqueMap.values()) as PredictionData[];
          combinedData.sort((a, b) => {
            const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
            const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
            return timeA - timeB;
          });
          
          console.log(`合并后共有 ${combinedData.length} 条预测数据`);
          return { 
            ...prev, 
            data: combinedData,
            isLoadingMore: false 
          };
        });
      } else {
        console.log('没有更多历史预测数据了');
        setState(prev => ({ ...prev, isLoadingMore: false }));
      }
    } catch (err) {
      console.error('加载历史预测数据失败:', err);
      setState(prev => ({ ...prev, isLoadingMore: false }));
    }
  }, []);

  // 首次加载和定时刷新
  useEffect(() => {
    // 首次加载数据
    loadData();

    // 设置定时刷新
    const interval = setInterval(refreshData, refreshInterval);

    // 清理定时器
    return () => {
      clearInterval(interval);
    };
  }, [loadData, refreshData, refreshInterval]);

  return {
    predictionData: state.data,
    isLoading: state.isLoading,
    isLoadingMore: state.isLoadingMore,
    error: state.error,
    loadData,
    refreshData,
    loadMoreHistory
  };
}; 