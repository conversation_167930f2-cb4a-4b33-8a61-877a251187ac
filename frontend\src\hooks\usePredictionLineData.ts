/**
 * 预测折线数据获取和管理Hook
 * 
 * 该Hook负责预测折线数据的获取、格式化和管理
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { fetchPredictionLines } from '../services/api';
import { Time } from 'lightweight-charts';

// 预测折线数据接口
export interface PredictionLineData {
  time: number;
  value: number;
  isActive: boolean;
}

// 图表格式化的预测折线数据点
export interface FormattedPredictionPoint {
  time: Time;
  value: number;
}

/**
 * 预测折线数据钩子
 * 负责管理预测折线数据的加载、更新和处理逻辑
 * @param isVisible 组件是否可见
 * @param refreshInterval 数据刷新间隔（毫秒）
 * @returns 预测折线数据相关状态和方法
 */
export function usePredictionLineData(isVisible: boolean, refreshInterval: number = 3600000) {
  // 状态管理
  const [predictionLineData, setPredictionLineData] = useState<PredictionLineData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formattedData, setFormattedData] = useState<FormattedPredictionPoint[]>([]);
  
  // 引用存储
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(false);
  
  // 加载预测折线数据
  const loadPredictionLineData = useCallback(async () => {
    if (!isMountedRef.current || !isVisible) return;
    
    setIsLoading(true);
    try {
      const data = await fetchPredictionLines();
      if (data && Array.isArray(data) && data.length > 0) {
        setPredictionLineData(data);
        console.log(`[预测数据] 获取到当前节气周期的 ${data.length} 条预测折线数据`);
      } else {
        console.warn('[预测数据] 获取到的当前节气周期预测折线数据为空');
      }
      setError(null);
    } catch (err) {
      console.error('[预测数据] 加载当前节气周期预测折线数据失败:', err);
      setError('获取预测折线数据失败');
    } finally {
      setIsLoading(false);
    }
  }, [isVisible]);
  
  // 格式化预测折线数据，转换为图表需要的格式
  const formatPredictionData = useCallback(() => {
    if (predictionLineData.length === 0) return;
    
    try {
      // 转换为图表需要的格式
      const lineData: FormattedPredictionPoint[] = predictionLineData.map(item => {
        // 确保时间戳格式正确 - 图表库需要秒级时间戳或者日期字符串
        const timeValue = typeof item.time === 'number' 
          ? Math.floor(item.time / 1000) as Time // 转换为秒级时间戳
          : item.time as Time;
        
        return {
          time: timeValue,
          value: item.value
        };
      });
      
      // 使用Map对象去除重复的时间戳，保留最后一个数据点
      const uniqueDataMap = new Map<number | string, FormattedPredictionPoint>();
      
      lineData.forEach(item => {
        const timeKey = typeof item.time === 'number' ? item.time : item.time.toString();
        uniqueDataMap.set(timeKey, item);
      });
      
      // 转换回数组
      const uniqueData = Array.from(uniqueDataMap.values());
      
      // 确保数据按时间顺序排列
      uniqueData.sort((a, b) => {
        const timeA = typeof a.time === 'number' ? a.time : new Date(a.time as string).getTime() / 1000;
        const timeB = typeof b.time === 'number' ? b.time : new Date(b.time as string).getTime() / 1000;
        return timeA - timeB;
      });
      
      setFormattedData(uniqueData);
      console.log('[预测数据] 当前节气周期预测折线数据格式化完成，共', uniqueData.length, '条数据点（去重后）');
      if (uniqueData.length < lineData.length) {
        console.log(`[预测数据] 注意：已移除 ${lineData.length - uniqueData.length} 个重复时间戳的数据点`);
      }
    } catch (error) {
      console.error('[预测数据] 格式化当前节气周期预测折线数据失败:', error);
    }
  }, [predictionLineData]);
  
  // 当原始数据更新时，格式化数据
  useEffect(() => {
    if (predictionLineData.length > 0) {
      formatPredictionData();
    }
  }, [predictionLineData, formatPredictionData]);
  
  // 组件挂载时设置isMounted标记
  useEffect(() => {
    isMountedRef.current = true;
    
    // 首次加载数据
    if (isVisible) {
      loadPredictionLineData();
    }
    
    return () => {
      isMountedRef.current = false;
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    };
  }, [isVisible, loadPredictionLineData]);
  
  // 设置定时刷新
  useEffect(() => {
    // 清除旧的定时器
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
      refreshIntervalRef.current = null;
    }
    
    // 只有在可见时才设置定时刷新
    if (isVisible && isMountedRef.current) {
      // 定时刷新
      refreshIntervalRef.current = setInterval(() => {
        loadPredictionLineData();
      }, refreshInterval);
      
      console.log(`[预测数据] 已设置当前节气周期预测折线数据定时刷新：${refreshInterval / 1000 / 60}分钟一次`);
    }
    
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    };
  }, [isVisible, refreshInterval, loadPredictionLineData]);
  
  // 手动刷新方法
  const refreshData = useCallback(() => {
    loadPredictionLineData();
  }, [loadPredictionLineData]);
  
  return {
    rawData: predictionLineData,
    formattedData,
    isLoading,
    error,
    refreshData
  };
} 