import { Request, Response } from 'express';
import systemSettingsService from '../services/systemSettingsService';

/**
 * 获取用户设置
 * 返回用户注册相关设置
 * GET /api/config/user-settings
 */
export const getUserSettings = async (req: Request, res: Response) => {
  try {
    // 获取系统设置
    const settings = await systemSettingsService.getSystemSettings();
    
    // 返回用户设置
    res.json({
      success: true,
      settings: {
        enableRegistration: settings.userSettings.enableRegistration,
        inviteCodeRequired: settings.userSettings.inviteCodeRequired,
        defaultRole: settings.userSettings.defaultRole,
        passwordMinLength: settings.userSettings.passwordMinLength,
        passwordRequireSpecialChar: settings.userSettings.passwordRequireSpecialChar,
        allowedEmailDomains: settings.userSettings.allowedEmailDomains,
        trialDays: settings.userSettings.trialDays,
        inviteRewardDays: settings.userSettings.inviteRewardDays
      }
    });
  } catch (error) {
    console.error('获取用户设置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

export default {
  getUserSettings
};
