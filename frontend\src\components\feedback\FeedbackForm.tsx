import React, { useState } from 'react';
import { Button } from '../../components/ui/button';
import { Card, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Textarea } from '../../components/ui/textarea';
import { submitFeedback } from '../../api/feedback';
import { useToast } from '../ui/use-toast';

interface FeedbackFormProps {
  onSuccess?: () => void;
}

const FeedbackForm: React.FC<FeedbackFormProps> = ({ onSuccess }) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // 表单验证
  const isFormValid = () => {
    if (!title.trim()) {
      toast({
        title: '请输入反馈标题',
        variant: 'destructive'
      });
      return false;
    }

    if (!content.trim()) {
      toast({
        title: '请输入反馈内容',
        variant: 'destructive'
      });
      return false;
    }

    if (content.length > 1000) {
      toast({
        title: '反馈内容不能超过1000个字符',
        variant: 'destructive'
      });
      return false;
    }

    return true;
  };

  // 提交反馈
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isFormValid()) return;

    try {
      setIsSubmitting(true);
      await submitFeedback({ title, content });

      toast({
        title: '反馈提交成功',
        description: '感谢您的反馈，我们会尽快处理',
        variant: 'default'
      });

      // 重置表单
      setTitle('');
      setContent('');

      // 调用成功回调
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('提交反馈失败:', error);
      toast({
        title: '提交失败',
        description: error instanceof Error ? error.message : '请稍后重试',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>提交反馈</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div className="space-y-2 text-left">
            <Label htmlFor="title" className="text-left">标题</Label>
            <Input
              id="title"
              placeholder="请简要描述您遇到的问题"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              maxLength={100}
              required
              className="text-left"
            />
          </div>
          <div className="space-y-2 text-left">
            <Label htmlFor="content" className="text-left">描述</Label>
            <Textarea
              id="content"
              placeholder="请详细描述您遇到的问题或建议"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              rows={5}
              required
              className="text-left"
            />
            <div className="text-xs text-content-muted text-right">
              {content.length}/1000
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? '提交中...' : '提交反馈'}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
};

export default FeedbackForm;
