/**
 * 时区管理器
 * 
 * 负责管理图表时区设置，提供时区转换和格式化功能
 */

// 时区选项定义
export interface TimezoneOption {
  id: string;        // 时区ID
  label: string;     // 显示名称
  offset: number;    // UTC偏移量（小时）
}

// 本地存储的键名
const TIMEZONE_STORAGE_KEY = 'chart_timezone_preference';

// 常用时区列表
export const TIMEZONE_OPTIONS: TimezoneOption[] = [
  { id: 'UTC', label: 'UTC（世界统一时间）', offset: 0 },
  { id: 'UTC-10', label: 'UTC-10（檀香山）', offset: -10 },
  { id: 'UTC-8', label: 'UTC-8（朱诺）', offset: -8 },
  { id: 'UTC-7-Phoenix', label: 'UTC-7（菲尼克斯）', offset: -7 },
  { id: 'UTC-7-LA', label: 'UTC-7（洛杉矶）', offset: -7 },
  { id: 'UTC-7-Vancouver', label: 'UTC-7（温哥华）', offset: -7 },
  { id: 'UTC-6-Denver', label: 'UTC-6（丹佛）', offset: -6 },
  { id: 'UTC-6-Mexico', label: 'UTC-6（墨西哥城）', offset: -6 },
  { id: 'UTC-5-Bogota', label: 'UTC-5（波哥大）', offset: -5 },
  { id: 'UTC-5-Lima', label: 'UTC-5（利马）', offset: -5 },
  { id: 'UTC-5-Chicago', label: 'UTC-5（芝加哥）', offset: -5 },
  { id: 'UTC-4-Toronto', label: 'UTC-4（多伦多）', offset: -4 },
  { id: 'UTC-4-NY', label: 'UTC-4（纽约）', offset: -4 },
  { id: 'UTC-4-Santiago', label: 'UTC-4（圣地亚哥）', offset: -4 },
  { id: 'UTC-3-SaoPaulo', label: 'UTC-3（圣保罗）', offset: -3 },
  { id: 'UTC-Reykjavik', label: 'UTC（雷克雅维克）', offset: 0 },
  { id: 'UTC+1-Lagos', label: 'UTC+1（拉哥斯）', offset: 1 },
  { id: 'UTC+1-Tunis', label: 'UTC+1（突尼斯）', offset: 1 },
  { id: 'UTC+2-Amsterdam', label: 'UTC+2（阿姆斯特丹）', offset: 2 },
  { id: 'UTC+2-Oslo', label: 'UTC+2（奥斯陆）', offset: 2 },
  { id: 'UTC+2-Paris', label: 'UTC+2（巴黎）', offset: 2 },
  { id: 'UTC+2-Berlin', label: 'UTC+2（柏林）', offset: 2 },
  { id: 'UTC+2-Belgrade', label: 'UTC+2（贝尔格莱德）', offset: 2 },
  { id: 'UTC+2-Budapest', label: 'UTC+2（布达佩斯）', offset: 2 },
  { id: 'UTC+2-Prague', label: 'UTC+2（布拉格）', offset: 2 },
  { id: 'UTC+2-Brussels', label: 'UTC+2（布鲁塞尔）', offset: 2 },
  { id: 'UTC+2-Copenhagen', label: 'UTC+2（哥本哈根）', offset: 2 },
  { id: 'UTC+2-Warsaw', label: 'UTC+2（华沙）', offset: 2 },
  { id: 'UTC+2-Luxembourg', label: 'UTC+2（卢森堡）', offset: 2 },
  { id: 'UTC+2-Rome', label: 'UTC+2（罗马）', offset: 2 },
  { id: 'UTC+2-Madrid', label: 'UTC+2（马德里）', offset: 2 },
  { id: 'UTC+2-Malta', label: 'UTC+2（马耳他）', offset: 2 },
  { id: 'UTC+2-Zurich', label: 'UTC+2（苏黎世）', offset: 2 },
  { id: 'UTC+2-Vienna', label: 'UTC+2（维也纳）', offset: 2 },
  { id: 'UTC+2-Johannesburg', label: 'UTC+2（约翰尼斯堡）', offset: 2 },
  { id: 'UTC+3-Bahrain', label: 'UTC+3（巴林）', offset: 3 },
  { id: 'UTC+3-Bucharest', label: 'UTC+3（布加勒斯特）', offset: 3 },
  { id: 'UTC+3-Helsinki', label: 'UTC+3（赫尔辛基）', offset: 3 },
  { id: 'UTC+3-Qatar', label: 'UTC+3（卡塔尔）', offset: 3 },
  { id: 'UTC+3-Cairo', label: 'UTC+3（开罗）', offset: 3 },
  { id: 'UTC+3-Kuwait', label: 'UTC+3（科威特）', offset: 3 },
  { id: 'UTC+3-Riga', label: 'UTC+3（里加）', offset: 3 },
  { id: 'UTC+3-Riyadh', label: 'UTC+3（利雅得）', offset: 3 },
  { id: 'UTC+3-Moscow', label: 'UTC+3（莫斯科）', offset: 3 },
  { id: 'UTC+3-Nairobi', label: 'UTC+3（内罗毕）', offset: 3 },
  { id: 'UTC+3-Nicosia', label: 'UTC+3（尼科西亚）', offset: 3 },
  { id: 'UTC+3-Tallinn', label: 'UTC+3（塔林）', offset: 3 },
  { id: 'UTC+3-Vilnius', label: 'UTC+3（维尔纽斯）', offset: 3 },
  { id: 'UTC+3-Athens', label: 'UTC+3（雅典）', offset: 3 },
  { id: 'UTC+3-Jerusalem', label: 'UTC+3（耶路撒冷）', offset: 3 },
  { id: 'UTC+4-Dubai', label: 'UTC+4（迪拜）', offset: 4 },
  { id: 'UTC+4-Muscat', label: 'UTC+4（马斯喀特）', offset: 4 },
  { id: 'UTC+5-Ashgabat', label: 'UTC+5（阿什哈巴德）', offset: 5 },
  { id: 'UTC+5-Karachi', label: 'UTC+5（卡拉奇）', offset: 5 },
  { id: 'UTC+6-Almaty', label: 'UTC+6（阿拉木图）', offset: 6 },
  { id: 'UTC+6-Dhaka', label: 'UTC+6（达卡）', offset: 6 },
  { id: 'UTC+7-HoChiMinh', label: 'UTC+7（胡志明市）', offset: 7 },
  { id: 'UTC+7-Bangkok', label: 'UTC+7（曼谷）', offset: 7 },
  { id: 'UTC+7-Jakarta', label: 'UTC+7（雅加达）', offset: 7 },
  { id: 'UTC+8-Manila', label: 'UTC+8（马尼拉）', offset: 8 },
  { id: 'UTC+8-Perth', label: 'UTC+8（铂斯）', offset: 8 },
  { id: 'UTC+8-Shanghai', label: 'UTC+8（上海）', offset: 8 },
  { id: 'UTC+8-Taipei', label: 'UTC+8（台北）', offset: 8 },
  { id: 'UTC+8-Singapore', label: 'UTC+8（新加坡）', offset: 8 },
  { id: 'UTC+8-HongKong', label: 'UTC+8（香港）', offset: 8 },
  { id: 'UTC+8-Beijing', label: 'UTC+8（北京）', offset: 8 },
  { id: 'UTC+9-Tokyo', label: 'UTC+9（东京）', offset: 9 },
  { id: 'UTC+9-Seoul', label: 'UTC+9（首尔）', offset: 9 },
  { id: 'UTC+10-Brisbane', label: 'UTC+10（布里斯班）', offset: 10 },
  { id: 'UTC+10-Sydney', label: 'UTC+10（悉尼）', offset: 10 },
  { id: 'UTC+11', label: 'UTC+11（诺福克岛）', offset: 11 },
  { id: 'UTC+12', label: 'UTC+12（新西兰）', offset: 12 },
  { id: 'UTC+13', label: 'UTC+13（托克劳群岛）', offset: 13 },
];

// 单例类，管理图表时区设置
export class TimezoneManager {
  private static instance: TimezoneManager;
  private currentTimezone: TimezoneOption;
  private listeners: Array<(timezone: TimezoneOption) => void> = [];

  private constructor() {
    // 从本地存储加载时区设置（如果有）
    this.currentTimezone = this.loadTimezoneFromStorage() ||
      TIMEZONE_OPTIONS.find(tz => tz.id === 'UTC+8-Beijing') ||
      TIMEZONE_OPTIONS.find(tz => tz.offset === 8) ||
      TIMEZONE_OPTIONS[0]; // 最终回退到UTC
  }

  /**
   * 从本地存储加载时区设置
   * @returns 存储的时区设置，如果没有则返回null
   */
  private loadTimezoneFromStorage(): TimezoneOption | null {
    try {
      const savedTimezoneId = localStorage.getItem(TIMEZONE_STORAGE_KEY);
      if (savedTimezoneId) {
        const savedTimezone = TIMEZONE_OPTIONS.find(tz => tz.id === savedTimezoneId);
        if (savedTimezone) {
          console.log(`从本地存储加载时区设置: ${savedTimezone.label}`);
          return savedTimezone;
        }
      }
    } catch (error) {
      console.warn('读取本地存储的时区设置失败:', error);
    }
    return null;
  }

  /**
   * 将时区设置保存到本地存储
   * @param timezoneId 要保存的时区ID
   */
  private saveTimezoneToStorage(timezoneId: string): void {
    try {
      localStorage.setItem(TIMEZONE_STORAGE_KEY, timezoneId);
      console.log(`已保存时区设置: ${timezoneId}`);
    } catch (error) {
      console.warn('保存时区设置到本地存储失败:', error);
    }
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): TimezoneManager {
    if (!TimezoneManager.instance) {
      TimezoneManager.instance = new TimezoneManager();
    }
    return TimezoneManager.instance;
  }

  /**
   * 获取当前时区设置
   */
  public getCurrentTimezone(): TimezoneOption {
    return this.currentTimezone;
  }

  /**
   * 设置时区
   * @param timezoneId 要设置的时区ID
   */
  public setTimezone(timezoneId: string): void {
    const timezone = TIMEZONE_OPTIONS.find(tz => tz.id === timezoneId);
    if (timezone && timezone.id !== this.currentTimezone.id) {
      this.currentTimezone = timezone;
      // 保存到本地存储
      this.saveTimezoneToStorage(timezone.id);
      // 通知所有监听器
      this.notifyListeners();
    }
  }

  /**
   * 添加时区变更监听器
   * @param listener 监听回调函数
   */
  public addListener(listener: (timezone: TimezoneOption) => void): void {
    this.listeners.push(listener);
  }

  /**
   * 移除时区变更监听器
   * @param listener 要移除的监听器
   */
  public removeListener(listener: (timezone: TimezoneOption) => void): void {
    this.listeners = this.listeners.filter(l => l !== listener);
  }

  /**
   * 通知所有监听器时区已变更
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.currentTimezone));
  }

  /**
   * 将UTC时间戳转换为当前时区的时间
   * @param timestamp UTC时间戳（秒）
   * @returns 当前时区的日期对象
   */
  public convertToCurrentTimezone(timestamp: number): Date {
    const date = new Date(timestamp * 1000);
    
    // 获取UTC时间的偏移量（分钟）
    const utcOffset = date.getTimezoneOffset();
    
    // 计算当前时区和目标时区之间的偏移差（毫秒）
    const offsetDiff = (utcOffset + this.currentTimezone.offset * 60) * 60 * 1000;
    
    // 返回调整后的日期
    return new Date(date.getTime() + offsetDiff);
  }

  /**
   * 格式化时间戳为当前时区的时间字符串
   * @param timestamp UTC时间戳（秒）
   * @param format 输出格式（默认显示日期和时间）
   * @returns 格式化的时间字符串
   */
  public formatTime(timestamp: number, format: 'full' | 'time' | 'date' | 'datetime' = 'full'): string {
    const date = this.convertToCurrentTimezone(timestamp);
    
    if (format === 'time') {
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    } else if (format === 'date') {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      });
    } else if (format === 'datetime') {
      // 自定义格式: yyyy-MM-dd HH:mm
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    } else {
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    }
  }

  /**
   * 获取当前时区的当前时间
   * @returns 格式化的时间字符串
   */
  public getCurrentTime(): string {
    const now = new Date();
    const utcMillis = now.getTime() + (now.getTimezoneOffset() * 60 * 1000);
    const targetMillis = utcMillis + (this.currentTimezone.offset * 60 * 60 * 1000);
    const targetDate = new Date(targetMillis);
    
    return targetDate.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  }
}

// 导出默认单例实例
export default TimezoneManager.getInstance(); 