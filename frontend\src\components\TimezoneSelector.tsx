import React, { useState, useEffect, useRef } from 'react';
import timezoneManager, { TIMEZONE_OPTIONS, TimezoneOption } from '../utils/timezoneManager';

/**
 * 时区选择器组件
 * 用于显示当前时区时间，并提供时区切换功能
 */
const TimezoneSelector: React.FC = () => {
  const [currentTime, setCurrentTime] = useState<string>('');
  const [currentTimezone, setCurrentTimezone] = useState<TimezoneOption>(timezoneManager.getCurrentTimezone());
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownListRef = useRef<HTMLDivElement>(null);
  
  // 更新当前时间
  useEffect(() => {
    // 初始化
    updateCurrentTime();
    
    // 每秒更新一次时间
    const intervalId = setInterval(updateCurrentTime, 1000);
    
    // 清理函数
    return () => clearInterval(intervalId);
  }, [currentTimezone]);
  
  // 监听点击事件，用于关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);
  
  // 设置下拉菜单高度以撑满图表底部
  useEffect(() => {
    if (!dropdownOpen || !dropdownListRef.current) return;
    
    // 计算下拉菜单到底部的高度
    const setDropdownHeight = () => {
      if (!dropdownListRef.current) return;
      
      // 获取下拉菜单的位置信息
      const rect = dropdownListRef.current.getBoundingClientRect();
      // 获取窗口高度
      const windowHeight = window.innerHeight;
      // 获取主图表容器（假设有一个id或类名为chart-container的元素）
      const chartContainer = document.querySelector('.chart-composite');
      
      if (chartContainer) {
        // 获取图表容器的底部位置
        const chartRect = chartContainer.getBoundingClientRect();
        const chartBottomPosition = chartRect.bottom;
        
        // 计算下拉菜单最大高度 (图表底部位置 - 下拉菜单顶部位置 - 底部边距)
        const maxHeight = chartBottomPosition - rect.top - 10;
        
        // 设置高度
        dropdownListRef.current.style.maxHeight = `${maxHeight}px`;
      } else {
        // 如果找不到图表容器，则使用窗口高度的80%作为最大高度
        const maxHeight = windowHeight * 0.8 - rect.top;
        dropdownListRef.current.style.maxHeight = `${maxHeight}px`;
      }
    };
    
    // 设置初始高度
    setDropdownHeight();
    
    // 监听窗口大小变化，动态调整高度
    window.addEventListener('resize', setDropdownHeight);
    return () => window.removeEventListener('resize', setDropdownHeight);
  }, [dropdownOpen]);
  
  // 更新当前时间函数
  const updateCurrentTime = () => {
    setCurrentTime(timezoneManager.getCurrentTime());
  };
  
  // 切换时区函数
  const switchTimezone = (timezone: TimezoneOption) => {
    timezoneManager.setTimezone(timezone.id);
    setCurrentTimezone(timezone);
    setDropdownOpen(false);
  };
  
  // 切换下拉菜单状态
  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
  };
  
  // TradingView风格的滚动条样式
  const scrollbarStyle = `
    .tradingview-scrollbar::-webkit-scrollbar {
      width: 2px;
      height: 2px;
    }
    
    .tradingview-scrollbar::-webkit-scrollbar-track {
      background: transparent;
    }
    
    .tradingview-scrollbar::-webkit-scrollbar-thumb {
      background: rgba(160, 160, 160, 0.25);
      border-radius: 1px;
    }
    
    .tradingview-scrollbar::-webkit-scrollbar-thumb:hover {
      background: rgba(160, 160, 160, 0.4);
    }
    
    .tradingview-scrollbar {
      scrollbar-width: thin;
      scrollbar-color: rgba(160, 160, 160, 0.25) transparent;
    }
  `;

  // 在移动设备上只显示时区代码，不显示时间
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 480;
  
  return (
    <div className="relative" ref={dropdownRef}>
      {/* 注入滚动条样式 */}
      <style>{scrollbarStyle}</style>
      
      {/* 当前时区显示按钮 */}
      <button
        className="flex items-center h-7 px-1.5 rounded text-content-secondary hover:bg-white/5 text-sm transition-colors"
        onClick={toggleDropdown}
        title={currentTimezone.label}
      >
        <span className="mr-1 hidden sm:inline">{currentTime}</span>
        <span className="font-medium">{currentTimezone.id.split('-')[0]}</span>
        <svg 
          className={`ml-1 w-3 h-3 transform transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      
      {/* 时区选择下拉菜单 */}
      {dropdownOpen && (
        <div 
          ref={dropdownListRef}
          className="absolute right-0 top-full mt-1 w-44 bg-[#1e1f1f] border border-[#2d2e30] rounded shadow-lg z-50 overflow-y-auto tradingview-scrollbar"
          style={{ 
            minHeight: '100px',
            maxHeight: '300px',
            right: '0',
            // 在小屏幕上靠左显示
            [window.innerWidth < 420 ? 'right' : 'left']: window.innerWidth < 420 ? '-40px' : 'auto'
          }}
        >
          <div className="py-1 h-full">
            {TIMEZONE_OPTIONS.map((timezone) => (
              <button
                key={timezone.id}
                className={`w-full text-left px-3 py-1.5 text-xs sm:text-sm ${
                  timezone.id === currentTimezone.id
                    ? 'bg-primary text-content-primary'
                    : 'text-[#D9D9D9] hover:bg-white/5'
                }`}
                onClick={() => switchTimezone(timezone)}
              >
                {timezone.label.split('（')[0]} {timezone.label.match(/（(.+)）/)?.[1]}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default TimezoneSelector; 