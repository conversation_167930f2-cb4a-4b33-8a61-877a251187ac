import React, { useEffect, useState } from 'react';
import { Link, Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../components/ui/dropdown-menu";
import { Button } from "../components/ui/button";
import { Globe, BarChart2 } from "lucide-react";

import configService, { SiteInfo } from '../services/configService';

// X (Twitter) 图标组件
const XIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);

// Telegram 图标组件
const TelegramIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
    <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
  </svg>
);

const PublicLayout: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [siteInfo, setSiteInfo] = useState<SiteInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 加载网站配置
  useEffect(() => {
    const loadSiteInfo = async () => {
      try {
        const config = await configService.getConfig();
        setSiteInfo(config.siteInfo);

        // 根据配置设置默认语言
        if (config.siteInfo.defaultLanguage && i18n.language !== config.siteInfo.defaultLanguage) {
          i18n.changeLanguage(config.siteInfo.defaultLanguage);
        }
      } catch (error) {
        console.error('加载网站配置失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSiteInfo();
  }, [i18n]);

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background-light flex flex-col">
      {/* 顶部导航栏 */}
      <header className="h-16 bg-background backdrop-blur-md border-b border-border">
        <div className="container mx-auto px-4 h-full">
          <div className="flex items-center justify-between h-full">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-2">
              {siteInfo?.logoUrl ? (
                <img
                  src={siteInfo.logoUrl.startsWith('http')
                    ? siteInfo.logoUrl
                    : siteInfo.logoUrl.startsWith('/uploads')
                      ? `${process.env.REACT_APP_API_URL || 'http://localhost:4000'}${siteInfo.logoUrl}`
                      : siteInfo.logoUrl}
                  alt={siteInfo?.siteName || t('app.title')}
                  className="h-12 w-auto"
                  onError={(e) => {
                    console.error('Logo加载失败:', e, siteInfo.logoUrl);
                    const target = e.target as HTMLImageElement;
                    target.onerror = null; // 防止无限循环
                    target.style.display = 'none'; // 隐藏图片
                  }}
                />
              ) : (
                <>
                  <BarChart2 className="h-8 w-8 text-primary" />
                  <span className="text-xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                    {siteInfo?.siteName || t('app.title')}
                  </span>
                </>
              )}
            </Link>

            {/* 导航链接 */}
            <nav className="hidden md:flex items-center space-x-1">
              <Link
                to="/features"
                className="px-4 py-2 text-content-secondary hover:text-content-primary hover:bg-white/5 rounded-lg transition-colors"
              >
                功能特点
              </Link>
              <Link
                to="/docs"
                className="px-4 py-2 text-content-secondary hover:text-content-primary hover:bg-white/5 rounded-lg transition-colors"
              >
                文档
              </Link>
              <Link
                to="/help"
                className="px-4 py-2 text-content-secondary hover:text-content-primary hover:bg-white/5 rounded-lg transition-colors"
              >
                帮助
              </Link>
            </nav>

            {/* 右侧功能区 */}
            <div className="flex items-center space-x-4">
              {/* 语言切换 */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="text-content-secondary hover:text-content-primary hover:bg-white/5">
                    <Globe className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="bg-background-card border-border">
                  <DropdownMenuItem onClick={() => changeLanguage('zh')} className="text-content-secondary hover:text-content-primary hover:bg-white/5">
                    中文
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => changeLanguage('en')} className="text-content-secondary hover:text-content-primary hover:bg-white/5">
                    English
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* 登录注册按钮 */}
              <Link
                to="/login"
                className="px-4 py-2 text-content-secondary hover:text-content-primary hover:bg-white/5 rounded-lg transition-colors"
              >
                登录
              </Link>
              <Link
                to="/register"
                className="px-4 py-2 bg-primary hover:bg-primary-dark text-primary-foreground rounded-lg transition-colors"
              >
                注册
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* 主内容区域 */}
      <main className="flex-1">
        <Outlet />
      </main>

      {/* 页脚 */}
      <footer className="bg-background backdrop-blur-md border-t border-border py-4">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-content-muted">
              {siteInfo?.copyright || `© ${new Date().getFullYear()} ${siteInfo?.siteName || t('app.title')}. All rights reserved.`}
            </div>
            <div className="flex items-center space-x-4">
              <a href="/privacy" className="text-sm text-content-muted hover:text-content-primary transition-colors">
                隐私政策
              </a>
              <a href="/terms" className="text-sm text-content-muted hover:text-content-primary transition-colors">
                服务条款
              </a>
              {siteInfo?.socialLinks?.twitter && (
                <a
                  href={siteInfo.socialLinks.twitter}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-content-muted hover:text-content-primary transition-colors"
                  title="Twitter"
                >
                  <XIcon className="h-5 w-5" />
                </a>
              )}
              {siteInfo?.socialLinks?.telegram && (
                <a
                  href={siteInfo.socialLinks.telegram}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-content-muted hover:text-content-primary transition-colors"
                  title="Telegram"
                >
                  <TelegramIcon className="h-5 w-5" />
                </a>
              )}
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PublicLayout;