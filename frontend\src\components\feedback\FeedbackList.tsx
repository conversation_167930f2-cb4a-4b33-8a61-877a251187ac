import React, { useState, useEffect } from 'react';
import { getUserFeedbacks } from '../../api/feedback';
import { Button } from '../../components/ui/button';
import { useToast } from '../ui/use-toast';
import FeedbackDetail from './FeedbackDetail';

interface Feedback {
  _id: string;
  title: string;
  content: string;
  status: 'pending' | 'processing' | 'replied';
  adminReply?: string;
  replyAt?: string;
  createdAt: string;
}

interface FeedbackListProps {
  refreshTrigger?: number;
}

const FeedbackList: React.FC<FeedbackListProps> = ({ refreshTrigger = 0 }) => {
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null);
  const { toast } = useToast();

  // 获取反馈列表
  const fetchFeedbacks = async () => {
    try {
      setIsLoading(true);
      const response = await getUserFeedbacks(page, 10);
      setFeedbacks(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (error) {
      console.error('获取反馈列表失败:', error);
      toast({
        title: '获取反馈列表失败',
        description: error instanceof Error ? error.message : '请稍后重试',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 初始加载和刷新
  useEffect(() => {
    fetchFeedbacks();
  }, [page, refreshTrigger]);

  // 查看反馈详情
  const handleViewDetail = (feedback: Feedback) => {
    setSelectedFeedback(feedback);
  };

  // 关闭详情弹窗
  const handleCloseDetail = () => {
    setSelectedFeedback(null);
  };

  // 渲染状态标签
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500">未处理</span>;
      case 'processing':
        return <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500">处理中</span>;
      case 'replied':
        return <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500">已回复</span>;
      default:
        return null;
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <>
      {/* 反馈记录 */}
      <div className="border border-border rounded-lg overflow-hidden">
        <div className="p-5 bg-card">
          <h3 className="text-lg font-semibold">反馈记录</h3>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : feedbacks.length === 0 ? (
          <div className="p-5 text-center text-content-secondary">
            暂无反馈记录
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-muted/50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-content-secondary whitespace-nowrap">提交时间</th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-content-secondary whitespace-nowrap">操作</th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-content-secondary whitespace-nowrap">状态</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-border">
                {feedbacks.map((feedback) => (
                  <tr key={feedback._id}>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      {formatDate(feedback.createdAt)}
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetail(feedback)}
                      >
                        查看详情
                      </Button>
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      {renderStatusBadge(feedback.status)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* 分页控制 */}
        {totalPages > 1 && (
          <div className="flex justify-between items-center p-4 border-t border-border">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage((prev) => Math.max(prev - 1, 1))}
              disabled={page === 1 || isLoading}
            >
              上一页
            </Button>
            <span className="text-sm text-content-secondary">
              第 {page} 页 / 共 {totalPages} 页
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={page === totalPages || isLoading}
            >
              下一页
            </Button>
          </div>
        )}
      </div>

      {/* 反馈详情弹窗 */}
      {selectedFeedback && (
        <FeedbackDetail
          feedback={selectedFeedback}
          onClose={handleCloseDetail}
        />
      )}
    </>
  );
};

export default FeedbackList;
