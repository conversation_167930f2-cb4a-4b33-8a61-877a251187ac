import axios from 'axios';
import { AxiosRequestConfig } from 'axios';
// 引入SOCKS代理支持
import { SocksProxyAgent } from 'socks-proxy-agent';
import configService from './configService';

/**
 * 币安API服务类
 * 负责与币安交易所API交互，获取K线数据
 */
class BinanceService {
  private baseUrl: string;  // 币安API基础URL
  private axiosInstance: any;  // Axios实例
  private apiEndpoints: string[] = [];  // 从配置服务获取
  private currentEndpointIndex: number = 0;
  private timeout: number = 30000;  // 默认超时时间，从配置服务获取
  private maxRetries: number = 3;   // 默认最大重试次数，从配置服务获取

  /**
   * 构造函数
   * 初始化服务实例，设置API基础URL和代理配置
   */
  constructor() {
    // 初始化时设置默认值，后续会从配置服务获取
    this.baseUrl = 'https://api.binance.com';

    // 创建SOCKS代理agent
    const socksAgent = new SocksProxyAgent('socks5://127.0.0.1:10808');

    // 创建带有SOCKS代理配置的axios实例
    this.axiosInstance = axios.create({
      baseURL: this.baseUrl,
      // 使用SOCKS代理
      httpAgent: socksAgent,
      httpsAgent: socksAgent,
      timeout: this.timeout,  // 超时时间
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache'
      }
    });

    // 初始化完成后，异步加载配置
    this.loadConfig();
  }

  /**
   * 从配置服务加载配置
   */
  private async loadConfig(): Promise<void> {
    try {
      // 获取币安API端点列表
      const endpoints = await configService.get('securitySettings.binanceApiUrls', [
        'https://api.binance.com',
        'https://api1.binance.com',
        'https://api2.binance.com',
        'https://api3.binance.com',
        'https://api-gcp.binance.com'
      ]);

      // 获取超时时间
      const timeout = await configService.get('securitySettings.binanceApiTimeout', 30000);

      // 获取最大重试次数
      const maxRetries = await configService.get('securitySettings.binanceApiRetryTimes', 3);

      // 更新实例属性
      this.apiEndpoints = endpoints;
      this.timeout = timeout;
      this.maxRetries = maxRetries;

      // 更新axios实例配置
      this.baseUrl = this.apiEndpoints[this.currentEndpointIndex];
      this.axiosInstance.defaults.baseURL = this.baseUrl;
      this.axiosInstance.defaults.timeout = this.timeout;

      console.log('币安API服务配置加载完成，使用端点:', this.baseUrl);
      console.log('超时时间:', this.timeout, 'ms');
      console.log('最大重试次数:', this.maxRetries);
    } catch (error) {
      console.error('加载币安API配置失败:', error);
    }
  }

  /**
   * 切换到下一个API端点
   * 在当前端点失败的情况下调用
   */
  private switchToNextEndpoint(): void {
    this.currentEndpointIndex = (this.currentEndpointIndex + 1) % this.apiEndpoints.length;
    this.baseUrl = this.apiEndpoints[this.currentEndpointIndex];
    this.axiosInstance.defaults.baseURL = this.baseUrl;
    console.log(`切换到API端点: ${this.baseUrl}`);
  }

  /**
   * 发送带有自动重试功能的API请求
   * 在请求失败时自动切换端点并重试
   */
  async sendRequest(path: string, params: any): Promise<any> {
    let retries = 0;
    // 使用配置的最大重试次数，但不超过可用端点数量
    const effectiveMaxRetries = Math.min(this.maxRetries, this.apiEndpoints.length);

    while (retries < effectiveMaxRetries) {
      try {
        const response = await this.axiosInstance.get(path, { params });
        return response;
      } catch (error: any) {
        retries++;
        console.error(`API请求失败 (尝试 ${retries}/${effectiveMaxRetries}):`, error.message);

        if (retries < effectiveMaxRetries) {
          this.switchToNextEndpoint();
        } else {
          throw error; // 所有重试都失败，抛出错误
        }
      }
    }
  }

  /**
   * 获取特定时间的分钟K线数据
   * 从币安API获取指定时间点附近的1分钟K线数据
   *
   * @param symbol 交易对符号，默认为BTCUSDT
   * @param targetTime 目标时间戳（毫秒）
   * @returns 返回格式化的单个K线数据对象
   */
  async getMinuteKlineByTime(symbol: string = 'BTCUSDT', targetTime: number): Promise<any> {
    try {
      // 设置查询时间范围，以确保能获取到目标时间的K线
      const startTime = targetTime - 60000;  // 目标时间前1分钟
      const endTime = targetTime + 120000;   // 目标时间后2分钟

      // 使用配置了代理的axios实例发送请求
      const response = await this.sendRequest('/api/v3/klines', {
        symbol,              // 交易对
        interval: '1m',      // 1分钟K线
        startTime,           // 开始时间
        endTime              // 结束时间
      });

      // 在返回的K线中查找目标时间对应的K线
      const targetKline = response.data.find((kline: any[]) =>
        kline[0] === targetTime || (kline[0] <= targetTime && targetTime < kline[6])
      );

      // 如果未找到对应K线，抛出错误
      if (!targetKline) {
        throw new Error(`未找到时间 ${targetTime} 对应的K线`);
      }

      // 格式化并返回找到的K线数据
      return {
        time: targetKline[0],       // 开盘时间
        open: targetKline[1],       // 开盘价
        high: targetKline[2],       // 最高价
        low: targetKline[3],        // 最低价
        close: targetKline[4],      // 收盘价
        volume: targetKline[5],     // 交易量
        closeTime: targetKline[6],  // 收盘时间
      };
    } catch (error) {
      console.error('获取特定分钟K线时出错:', error);
      throw new Error('获取特定分钟K线失败');
    }
  }

  /**
   * 从币安API获取1分钟K线数据
   * 使用配置了SOCKS代理的Axios实例，带有自动重试功能
   *
   * @param symbol 交易对符号，默认为BTCUSDT
   * @param startTime 开始时间（毫秒时间戳）
   * @param endTime 结束时间（毫秒时间戳）
   * @param limit 返回数量限制，默认为300
   * @returns 1分钟K线数据数组
   */
  async fetchMinuteKline(
    symbol: string = 'BTCUSDT',
    startTime: number,
    endTime: number,
    limit: number = 300
  ): Promise<any[]> {
    try {
      console.log(`从币安API获取1分钟K线数据: ${symbol} ${new Date(startTime).toISOString()} -> ${new Date(endTime).toISOString()}`);

      // 使用类中已配置了代理的axios实例和重试机制
      const response = await this.sendRequest('/api/v3/klines', {
        symbol: symbol.toUpperCase(),
        interval: '1m',
        startTime,
        endTime,
        limit
      });

      // 添加请求延迟，避免触发API限制
      await new Promise(resolve => setTimeout(resolve, 1200));

      // 检查API响应
      if (!response.data || !Array.isArray(response.data)) {
        console.error('币安API返回格式无效:', response.data);
        return [];
      }

      console.log(`成功获取${response.data.length}条1分钟K线数据`);
      return response.data;
    } catch (error) {
      console.error(`币安API请求失败: /api/v3/klines`, error);
      return [];
    }
  }
}

// 导出服务实例
export default new BinanceService();

