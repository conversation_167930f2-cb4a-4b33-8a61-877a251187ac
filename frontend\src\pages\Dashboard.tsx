import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import MainKlineChart from '../components/MainKlineChart';
import useUserStore from '../store/useUserStore';
import { getSubscriptionInfo, SubscriptionInfo } from '../api/subscription';
import { Button } from '../components/ui/button';

// 仪表盘组件
const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useUserStore();
  const [subscription, setSubscription] = useState<SubscriptionInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 检查用户是否有有效订阅或处于试用期
  const hasValidAccess = () => {
    // 加载中时不进行判断
    if (isLoading) return true;
    
    // 如果是管理员，始终可以访问
    if (user?.role === 'admin') return true;
    
    // 检查是否有有效订阅或在试用期内
    return subscription?.status === 'active' || subscription?.status === 'trial';
  };

  // 获取订阅信息
  useEffect(() => {
    const fetchSubscriptionInfo = async () => {
      try {
        setIsLoading(true);
        const response = await getSubscriptionInfo();
        setSubscription(response.subscription);
      } catch (error) {
        console.error('获取订阅信息失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      fetchSubscriptionInfo();
    }
  }, [user]);

  return (
    <div className="min-h-screen bg-background text-content-primary relative">
      {/* 页面主体内容 */}
      <main className="container mx-auto">
        {/* 图表区域 */}
        <div className="relative">
          <MainKlineChart 
            showLoadingIndicator={false}
            symbol="BTC/USDT"
            timeframe="30M"
          />
          
          {/* 订阅遮罩 - 当没有有效访问权限时显示 */}
          {!hasValidAccess() && (
            <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex flex-col items-center justify-center z-10">
              <div className="text-center max-w-lg px-6 py-10 bg-card rounded-lg shadow-lg border border-border">
                <h3 className="text-2xl font-bold mb-4">请订阅以访问完整功能</h3>
                <p className="text-content-secondary mb-6">
                  您的{subscription?.status === 'inactive' ? '试用期已结束' : '账户未订阅'}。
                  订阅后即可获得完整的数据分析功能。
                </p>
                
                <Button
                  size="lg"
                  className="bg-primary text-white"
                  onClick={() => navigate('/subscribe')}
                >
                  立即订阅
                </Button>
              </div>
            </div>
          )}
        </div>
      </main>
      {/* 页面底部 */}

    </div>
  );
};

export default Dashboard; 